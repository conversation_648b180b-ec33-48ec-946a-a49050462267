<?php

namespace app\system\controller;

use app\common\core\base\BaseAdminController;
use app\common\lib\upload\UploadService;
use app\system\service\AttachmentService;
use app\system\service\ConfigService;
use think\response\Json;

/**
 * 附件管理控制器 - 重构版本
 * 支持文件去重、权限控制、多存储方式
 */
class AttachmentController extends BaseAdminController
{
	/**
	 * @var AttachmentService
	 */
	private AttachmentService $service;
	
	/**
	 * @var UploadService
	 */
	private UploadService $uploadService;
	
	public function initialize(): void
	{
		parent::initialize();
		$this->service       = AttachmentService::getInstance();
		$this->uploadService = new UploadService();
	}
	
	/**
	 * 获取文件列表
	 *
	 * @return Json
	 */
	public function index(): Json
	{
		try {
			$params = input();
			$result = $this->service->getList($params);
			
			return $this->success('获取成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 获取文件详情
	 *
	 * @param int $id 文件ID
	 * @return Json
	 */
	public function read(int $id): Json
	{
		try {
			$result = $this->service->getDetail($id);
			
			return $this->success('获取成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 文件上传
	 *
	 * @return Json
	 */
	public function upload(): Json
	{
		try {
			$file = request()->file('file');
			if (!$file) {
				return $this->error('请选择要上传的文件');
			}
			
			$storage = input('storage', 'local');
			$cateId  = input('cate_id/d', 0);
			
			// 转换为标准文件数组格式
			$fileInfo = [
				'name'     => $file->getOriginalName(),
				'type'     => $file->getOriginalMime(),
				'tmp_name' => $file->getPathname(),
				'size'     => $file->getSize()
			];
			
			$result = $this->uploadService->uploadFile($fileInfo, $storage, $cateId, $this->tenantId, $this->adminId);
			
			return $this->success('上传成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	
	/**
	 * 重命名文件
	 *
	 * @param int $id 文件ID
	 * @return Json
	 */
	public function rename(int $id): Json
	{
		try {
			$newName = input('name', '');
			if (empty($newName)) {
				return $this->error('文件名不能为空');
			}
			
			$result = $this->service->renameFile($id, $newName);
			
			return $result
				? $this->success('重命名成功')
				: $this->error('重命名失败');
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 移动文件
	 *
	 * @param int $id 文件ID
	 * @return Json
	 */
	public function move(int $id): Json
	{
		try {
			$cateId = input('cate_id/d', 0);
			
			$result = $this->service->moveFile($id, $cateId);
			
			return $result
				? $this->success('移动成功')
				: $this->error('移动失败');
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 复制文件
	 *
	 * @param int $id 文件ID
	 * @return Json
	 */
	public function copy(int $id): Json
	{
		try {
			$targetCateId = input('target_cate_id/d', 0);
			$newName      = input('new_name', '');
			
			$result = $this->service->copyFile($id, $targetCateId, $newName);
			
			return $result
				? $this->success('复制成功')
				: $this->error('复制失败');
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 删除文件
	 *
	 * @return Json
	 */
	public function delete(): Json
	{
		try {
			$ids = input('ids');
			if (empty($ids)) {
				return $this->error('请选择要删除的文件');
			}
			
			if (!is_array($ids)) {
				$ids = [$ids];
			}
			
			$result = $this->service->batchDelete($ids);
			
			if ($result['failed_count'] > 0) {
				return $this->error("删除完成，但有 {$result['failed_count']} 个文件删除失败");
			}
			
			return $this->success('删除成功');
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 批量移动文件
	 *
	 * @return Json
	 */
	public function batchMove(): Json
	{
		try {
			$fileIds = input('file_ids');
			$cateId  = input('cate_id/d', 0);
			
			if (empty($fileIds) || !is_array($fileIds)) {
				return $this->error('请选择要移动的文件');
			}
			
			$result = $this->service->batchMoveFiles($fileIds, $cateId);
			
			if ($result['failed_count'] > 0) {
				return $this->error("移动完成，但有 {$result['failed_count']} 个文件移动失败");
			}
			
			return $this->success('批量移动成功');
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 获取用户文件统计
	 *
	 * @return Json
	 */
	public function stats(): Json
	{
		try {
			$result = $this->service->getUserStats();
			
			return $this->success('获取成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 获取上传Token（云存储）
	 *
	 * @return Json
	 */
	public function getUploadToken(): Json
	{
		try {
			$storage = input('storage', 'qnoss');
			$params  = input('params', []);
			
			$result = $this->uploadService->getUploadToken($storage, $this->tenantId, $params);
			
			return $this->success('获取成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 处理云存储上传回调
	 *
	 * @param string $storage 存储类型
	 * @return Json
	 */
	public function uploadCallback(string $storage): Json
	{
		try {
			$params = input();
			
			$result = $this->uploadService->handleCallback($storage, $params, $this->tenantId, $this->adminId);
			
			return $this->success('处理成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 文件下载
	 *
	 * @param int $id 文件ID
	 * @return mixed
	 */
	public function download(int $id)
	{
		try {
			
			$fileDetail = $this->service->getDetail($id);
			
			// 根据存储类型处理下载
			if ($fileDetail['storage'] === 'local') {
				$filePath = public_path() . $fileDetail['path'];
				if (!file_exists($filePath)) {
					return $this->error('文件不存在');
				}
				
				return download($filePath, $fileDetail['display_name']);
			}
			else {
				// 云存储文件重定向到CDN地址
				return redirect($fileDetail['url']);
			}
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	
	public function getUploadConfig(): Json
	{
		$uploadInfo = ConfigService::getInstance()
		                           ->getInfo('upload');
		$type       = $uploadInfo['upload_allow_type'] ?? 'local';
		$data       = [
			'upload_allow_type' => $type,
			'upload_allow_ext'  => $uploadInfo['upload_allow_ext'] ?? '',
			'upload_allow_mime' => generateCategoryMap($uploadInfo['upload_allow_ext']),
			'upload_allow_size' => intval($uploadInfo['upload_allow_size'] ?? 2),
			'max_file_count'    => intval($uploadInfo['max_file_count'] ?? 5),
			'domain'            => '',
		];
		
		$typeArr = [
			'qnoss',
			'alioss',
			'txoss'
		];
		if (in_array($type, $typeArr)) {
			$data['domain'] = $uploadInfo[$type . '_domain'] ?? '';
		}
		
		return $this->success('获取成功', $data);
	}
	
}