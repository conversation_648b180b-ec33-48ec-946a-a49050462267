<template>
  <div class="business-trip-form-view">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="出差天数">
        {{ formData.duration || 0 }} 天
      </el-descriptions-item>

      <el-descriptions-item label="同行人">
        <DepartmentPersonForm
          v-model="companionsData"
          :multiple="true"
          :disabled="true"
          :collapse-tags="false"
          :collapse-tags-tooltip="false"
          placeholder="无同行人"
        />
      </el-descriptions-item>

      <el-descriptions-item label="出差事由" :span="2">
        {{ formData.purpose || '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="备注" :span="2" v-if="formData.remark">
        {{ formData.remark }}
      </el-descriptions-item>

      <!-- 附件展示 -->
      <el-descriptions-item
        label="附件"
        :span="2"
        v-if="attachmentList && attachmentList.length > 0"
      >
        <div class="attachment-list">
          <div v-for="(file, index) in attachmentList" :key="index" class="attachment-item">
            <el-link :href="file.url" target="_blank" type="primary">
              <el-icon>
                <Document />
              </el-icon>
              {{ file.name }}
            </el-link>
            <span class="file-size" v-if="file.size">({{ formatFileSize(file.size) }})</span>
          </div>
        </div>
      </el-descriptions-item>

      <!-- 行程明细 -->
      <el-descriptions-item
        label="行程明细"
        :span="2"
        v-if="formData.items && formData.items.length > 0"
      >
        <el-table :data="formData.items" border size="default">
          <el-table-column label="交通工具" width="120">
            <template #default="{ row }">
              {{ getTransportTypeText(row.transport_type) || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="单程往返" width="120">
            <template #default="{ row }">
              {{ getTripModeText(row.trip_mode) || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="departure_city" label="出发地" />
          <el-table-column prop="destination_city" label="目的地" />
          <el-table-column prop="start_time" label="开始时间" width="160" />
          <el-table-column prop="end_time" label="结束时间" width="160" />
          <el-table-column label="时长">
            <template #default="{ row }">
              <span style="font-size: 14px; font-weight: 500">{{ row.duration || 0 }}天</span>
            </template>
          </el-table-column>
        </el-table>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import {
    ElDescriptions,
    ElDescriptionsItem,
    ElTable,
    ElTableColumn,
    ElLink,
    ElIcon
  } from 'element-plus'
  import { Document } from '@element-plus/icons-vue'
  import DepartmentPersonForm from '@/components/custom/DepartmentPersonForm.vue'

  // 组件属性定义
  const props = defineProps({
    // 表单数据
    formData: {
      type: Object,
      required: true
    },
    // 业务代码
    businessCode: {
      type: String,
      default: 'hr_business_trip'
    }
  })

  /**
   * 处理同行人数据格式
   */
  const companionsData = computed(() => {
    const companions = props.formData.companions
    if (!Array.isArray(companions)) {
      return []
    }

    // 转换为 DepartmentPersonForm 期望的格式
    return companions.map((companion: any) => {
      if (typeof companion === 'object' && companion.id) {
        // 如果是完整的用户对象，只保留 id
        return { id: companion.id }
      }
      return companion
    })
  })

  /**
   * 处理附件列表
   */
  const attachmentList = computed(() => {
    const attachment = props.formData.attachment
    if (!attachment) {
      return []
    }

    // 如果已经是数组格式，转换为对象格式
    if (Array.isArray(attachment)) {
      return attachment.map((url) => ({
        name: typeof url === 'string' ? url.split('/').pop() || 'unknown' : url.name || 'unknown',
        url: typeof url === 'string' ? url : url.url,
        size: typeof url === 'object' ? url.size || 0 : 0
      }))
    }

    // 如果是字符串格式（逗号分隔的URL），转换为数组
    if (typeof attachment === 'string') {
      const urls = attachment.split(',').filter((url) => url.trim())
      return urls.map((url) => ({
        name: url.split('/').pop() || 'unknown',
        url: url.trim(),
        size: 0
      }))
    }

    return []
  })

  /**
   * 格式化文件大小
   */
  const formatFileSize = (size: number) => {
    if (!size) return '0 B'
    const units = ['B', 'KB', 'MB', 'GB']
    let index = 0
    while (size >= 1024 && index < units.length - 1) {
      size /= 1024
      index++
    }
    return `${size.toFixed(1)} ${units[index]}`
  }

  /**
   * 获取交通工具文本
   */
  const getTransportTypeText = (value: number) => {
    const options: Record<number, string> = {
      1: '飞机',
      2: '高铁',
      3: '火车',
      4: '汽车',
      5: '其他'
    }
    return options[value] || ''
  }

  /**
   * 获取单程往返文本
   */
  const getTripModeText = (value: number) => {
    const options: Record<number, string> = {
      1: '往返',
      2: '单程'
    }
    return options[value] || ''
  }
</script>

<style scoped lang="scss">
  .attachment-list {
    .attachment-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .el-link {
        margin-right: 8px;

        .el-icon {
          margin-right: 4px;
        }
      }

      .file-size {
        color: #909399;
        font-size: 12px;
      }
    }
  }
</style>
