<template>
  <div class="companions-selector">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-placeholder">
      <el-skeleton :rows="1" animated />
    </div>

    <!-- 触发器 -->
    <el-select
      v-else
      v-model="displayValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :size="size"
      :clearable="clearable"
      :multiple="multiple"
      :collapse-tags="collapseTags"
      :collapse-tags-tooltip="collapseTagsTooltip"
      :filterable="false"
      @click="openSelector"
      @clear="handleClear"
      @remove-tag="handleRemoveTag"
    >
      <template #empty>
        <div style="text-align: center; padding: 10px">
          <el-button type="primary" size="small" @click="openSelector"> 选择同行人 </el-button>
        </div>
      </template>
    </el-select>

    <!-- 选择器对话框 -->
    <DepartmentPersonSelector
      v-model="selectorVisible"
      :title="selectorTitle"
      :multiple="multiple"
      :selected="selectedPersonsData"
      @confirm="handleConfirm"
      @cancel="selectorVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import DepartmentPersonSelector from '@/components/custom/DepartmentPersonSelector.vue'
import { WorkflowApi } from '@/components/custom/workflow/api/workflowApi'

// 简化的数据类型定义
interface PersonItem {
  id: string | number
}

// 内部使用的完整人员信息
interface FullPersonItem {
  id: string | number
  name: string
  avatar?: string
  position?: string
  department?: string
  [key: string]: any
}

// Props 定义
interface Props {
  modelValue?: PersonItem[] | PersonItem | null
  multiple?: boolean
  placeholder?: string
  disabled?: boolean
  size?: 'large' | 'default' | 'small'
  clearable?: boolean
  collapseTags?: boolean
  collapseTagsTooltip?: boolean
  selectorTitle?: string
}

const props = withDefaults(defineProps<Props>(), {
  multiple: false,
  placeholder: '请选择同行人',
  disabled: false,
  size: 'default',
  clearable: true,
  collapseTags: true,
  collapseTagsTooltip: true,
  selectorTitle: '选择同行人'
})

// Emits 定义
interface Emits {
  'update:modelValue': [value: PersonItem[] | PersonItem | null]
  change: [value: PersonItem[] | PersonItem | null]
}

const emit = defineEmits<Emits>()

// 响应式数据
const selectorVisible = ref(false)
const personInfoCache = ref<Map<string | number, FullPersonItem>>(new Map())
const isLoading = ref(false)

// 计算属性
const selectedPersonsData = computed(() => {
  if (!props.modelValue) return props.multiple ? [] : null

  const getPersonInfo = (id: string | number): FullPersonItem | null => {
    return personInfoCache.value.get(id) || { id, name: `用户${id}` }
  }

  if (props.multiple) {
    const items = Array.isArray(props.modelValue) ? props.modelValue : [props.modelValue]
    return items
      .map((item) => getPersonInfo(item.id))
      .filter((person): person is FullPersonItem => person !== null)
  } else {
    const item = Array.isArray(props.modelValue) ? props.modelValue[0] : props.modelValue
    return item ? getPersonInfo(item.id) : null
  }
})

const displayValue = computed({
  get: () => {
    if (!props.modelValue) return props.multiple ? [] : ''

    if (props.multiple) {
      const items = Array.isArray(props.modelValue) ? props.modelValue : [props.modelValue]
      return items.map((item) => {
        const personInfo = personInfoCache.value.get(item.id)
        return {
          label: personInfo?.name || `用户${item.id}`,
          value: item.id
        }
      })
    } else {
      const item = Array.isArray(props.modelValue) ? props.modelValue[0] : props.modelValue
      if (item) {
        const personInfo = personInfoCache.value.get(item.id)
        return personInfo?.name || `用户${item.id}`
      }
      return ''
    }
  },
  set: () => {
    // 不允许直接设置，只能通过选择器
  }
})

// 方法
const openSelector = () => {
  if (!props.disabled) {
    selectorVisible.value = true
  }
}

const handleConfirm = (selected: FullPersonItem[] | FullPersonItem | null) => {
  console.log('CompanionsSelector handleConfirm received:', selected)

  // 缓存人员信息
  if (selected) {
    if (Array.isArray(selected)) {
      selected.forEach((person) => {
        personInfoCache.value.set(person.id, person)
      })
    } else {
      personInfoCache.value.set(selected.id, selected)
    }
  }

  // 转换为简化格式
  let result: PersonItem[] | PersonItem | null = null

  if (props.multiple) {
    result = Array.isArray(selected)
      ? selected.map((person) => ({ id: person.id }))
      : selected
        ? [{ id: selected.id }]
        : []
  } else {
    result = Array.isArray(selected)
      ? selected.length > 0
        ? { id: selected[0].id }
        : null
      : selected
        ? { id: selected.id }
        : null
  }

  console.log('CompanionsSelector emitting result:', result)
  emit('update:modelValue', result)
  emit('change', result)
}

const handleClear = () => {
  const value = props.multiple ? [] : null
  emit('update:modelValue', value)
  emit('change', value)
}

const handleRemoveTag = (tag: any) => {
  if (!props.multiple || !Array.isArray(props.modelValue)) return

  const newValue = props.modelValue.filter((item) => item.id !== tag.value)
  emit('update:modelValue', newValue)
  emit('change', newValue)
}

// 预加载用户信息的方法
const preloadUserInfo = (users: FullPersonItem[]) => {
  console.log('CompanionsSelector: 预加载用户信息', users)
  users.forEach((user) => {
    const userInfo = {
      id: user.id,
      name: user.name,
      avatar: user.avatar,
      position: user.position,
      department: user.department
    }
    personInfoCache.value.set(user.id, userInfo)
    personInfoCache.value.set(String(user.id), userInfo)
  })
  isLoading.value = false
}

// 监听 modelValue 变化，预加载人员信息
watch(
  () => props.modelValue,
  () => {
    // 对于 CompanionsSelector，我们不需要发起 API 请求
    // 因为数据会通过 preloadUserInfo 方法传入
    isLoading.value = false
  },
  { immediate: true, deep: true }
)

// 暴露方法给父组件
defineExpose({
  preloadUserInfo
})
</script>

<style lang="scss" scoped>
.companions-selector {
  width: 100%;

  :deep(.el-select) {
    width: 100%;
  }

  :deep(.el-select__tags) {
    max-width: calc(100% - 30px);
  }

  :deep(.el-tag) {
    max-width: 120px;

    .el-tag__content {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.loading-placeholder {
  width: 100%;
  min-height: 32px;
  display: flex;
  align-items: center;
}
</style>
