<?php
declare(strict_types=1);

namespace app\system\model;

use app\common\core\base\BaseModel;
use app\common\utils\BitFormat;
use think\model\relation\BelongsTo;

/**
 * 用户文件关联模型
 * 管理用户文件权限和显示信息
 */
class AttachmentUserModel extends BaseModel
{
	
	// 设置表名
	protected $name = 'system_attachment_user';
	
	// 设置字段信息
	protected $schema = [
		'id'            => 'int',
		'attachment_id' => 'int',
		'user_id'       => 'int',
		'tenant_id'     => 'int',
		'cate_id'       => 'int',
		'display_name'  => 'string',
		'original_name' => 'string',
		'upload_ip'     => 'string',
		'upload_source' => 'string',
		'creator_id'    => 'int',
		'created_at'    => 'datetime',
		'updated_at'    => 'datetime',
		'deleted_at'    => 'datetime',
	];
	
	// 字段类型转换
	protected $type = [
		'id'            => 'integer',
		'attachment_id' => 'integer',
		'user_id'       => 'integer',
		'tenant_id'     => 'integer',
		'cate_id'       => 'integer',
		'creator_id'    => 'integer',
		'created_at'    => 'datetime',
		'updated_at'    => 'datetime',
		'deleted_at'    => 'datetime',
	];
	
	/**
	 * 关联物理文件
	 */
	public function attachment(): BelongsTo
	{
		return $this->belongsTo(AttachmentModel::class, 'attachment_id', 'id');
	}
	
	/**
	 * 关联用户
	 */
	public function user(): BelongsTo
	{
		return $this->belongsTo(AdminModel::class, 'user_id', 'id');
	}
	
	/**
	 * 关联创建者
	 */
	public function creator(): BelongsTo
	{
		return $this->belongsTo(AdminModel::class, 'creator_id', 'id');
	}
	
	/**
	 * 获取用户文件列表
	 */
	public static function getUserFiles(array $params = []): array
	{
		$userId = request()->adminId;
		$query  = self::alias('au')
		              ->join('system_attachment a', 'au.attachment_id = a.id')
		              ->where('au.user_id', $userId);
		
		// 分类筛选
		if (isset($params['cate_id']) && $params['cate_id'] >= 0) {
			$query->where('au.cate_id', $params['cate_id']);
		}
		
		// 文件名搜索
		if (!empty($params['name'])) {
			$query->where('au.display_name', 'like', '%' . $params['name'] . '%');
		}
		
		// 存储类型筛选
		if (!empty($params['storage'])) {
			$query->where('a.storage', $params['storage']);
		}
		
		// 文件扩展名筛选
		if (!empty($params['extension'])) {
			$query->where('a.extension', $params['extension']);
		}
		
		// 选择字段
		$query->field([
			'au.id',
			'au.cate_id',
			'au.display_name',
			'au.original_name',
			'au.upload_source',
			'au.created_at as upload_time',
			'a.name',
			'a.path',
			'a.size',
			'a.extension',
			'a.mime_type',
			'a.storage',
			'a.ref_count'
		]);
		
		// 排序
		$query->order('au.created_at desc');
		
		// 分页
		$page  = $params['page'] ?? 1;
		$limit = $params['limit'] ?? 10;
		
		return $query->paginate([
			'page'      => $page,
			'list_rows' => $limit
		])
		             ->toArray();
	}
	
	/**
	 * 检查用户是否有文件权限
	 */
	public static function checkUserFileAccess(int $fileId, int $userId, int $tenantId): bool
	{
		$result = self::where('id', $fileId)
		              ->where('user_id', $userId)
		              ->where('tenant_id', $tenantId)
		              ->where('deleted_at', null)
		              ->find();
		
		return !is_null($result);
	}
	
	/**
	 * 创建用户文件关联
	 */
	public static function createUserFileAssociation(array $data): self
	{
		return self::create([
			'attachment_id' => $data['attachment_id'],
			'user_id'       => $data['user_id'],
			'tenant_id'     => $data['tenant_id'],
			'cate_id'       => $data['cate_id'] ?? 0,
			'display_name'  => $data['display_name'],
			'original_name' => $data['original_name'],
			'upload_ip'     => $data['upload_ip'] ?? request()->ip(),
			'upload_source' => $data['upload_source'] ?? 'web',
			'creator_id'    => $data['creator_id'] ?? $data['user_id'],
		]);
	}
	
	/**
	 * 获取用户文件统计
	 */
	public static function getUserFileStats(): array
	{
		$userId = request()->adminId;
		$query  = self::alias('au')
		              ->join('system_attachment a', 'au.attachment_id = a.id')
		              ->where('au.user_id', $userId);
		
		$stats = $query->field([
			'COUNT(*) as total_files',
			'SUM(a.size) as total_size'
		])
		               ->find();
		
		// 按分类统计
		$categoryStats = $query->field([
			'au.cate_id',
			'COUNT(*) as file_count',
			'SUM(a.size) as total_size'
		])
		                       ->group('au.cate_id')
		                       ->select();
		
		// 按扩展名统计
		$extensionStats = $query->field([
			'a.extension',
			'COUNT(*) as file_count',
			'SUM(a.size) as total_size'
		])
		                        ->group('a.extension')
		                        ->select();
		
		return [
			'total_files'          => $stats['total_files'] ?? 0,
			'total_size'           => $stats['total_size'] ?? 0,
			'total_size_formatted' => BitFormat::formatSizeAuto((int)($stats['total_size'] ?? 0)),
			'by_category'          => $categoryStats->toArray(),
			'by_extension'         => $extensionStats->toArray(),
		];
	}
	
	/**
	 * 批量删除用户文件
	 */
	public static function batchDeleteUserFiles(array $fileIds, int $userId, int $tenantId): array
	{
		$successCount = 0;
		$failedCount  = 0;
		$failedIds    = [];
		
		foreach ($fileIds as $fileId) {
			try {
				$userFile = self::where('id', $fileId)
				                ->where('user_id', $userId)
				                ->where('tenant_id', $tenantId)
				                ->find();
				
				if ($userFile) {
					$userFile->delete();
					$successCount++;
				}
				else {
					$failedCount++;
					$failedIds[] = $fileId;
				}
			}
			catch (\Exception $e) {
				$failedCount++;
				$failedIds[] = $fileId;
			}
		}
		
		return [
			'success_count' => $successCount,
			'failed_count'  => $failedCount,
			'failed_ids'    => $failedIds
		];
	}
}
