<?php
declare(strict_types=1);

namespace app\system\model;

use app\common\core\base\BaseModel;
use app\common\utils\BitFormat;
use think\model\relation\BelongsTo;

/**
 * 用户文件关联模型
 * 管理用户文件权限和显示信息
 */
class AttachmentUserModel extends BaseModel
{
	
	// 设置表名
	protected $name = 'system_attachment_user';
	
	// 设置字段信息
	protected $schema = [
		'id'            => 'int',
		'attachment_id' => 'int',
		'user_id'       => 'int',
		'tenant_id'     => 'int',
		'cate_id'       => 'int',
		'display_name'  => 'string',
		'original_name' => 'string',
		'upload_ip'     => 'string',
		'upload_source' => 'string',
		'creator_id'    => 'int',
		'created_at'    => 'datetime',
		'updated_at'    => 'datetime',
		'deleted_at'    => 'datetime',
	];
	
	// 字段类型转换
	protected $type = [
		'id'            => 'integer',
		'attachment_id' => 'integer',
		'user_id'       => 'integer',
		'tenant_id'     => 'integer',
		'cate_id'       => 'integer',
		'creator_id'    => 'integer',
		'created_at'    => 'datetime',
		'updated_at'    => 'datetime',
		'deleted_at'    => 'datetime',
	];
	
	/**
	 * 关联物理文件
	 */
	public function attachment(): BelongsTo
	{
		return $this->belongsTo(AttachmentModel::class, 'attachment_id', 'id');
	}
	
	/**
	 * 关联用户
	 */
	public function user(): BelongsTo
	{
		return $this->belongsTo(AdminModel::class, 'user_id', 'id');
	}
	
	/**
	 * 关联创建者
	 */
	public function creator(): BelongsTo
	{
		return $this->belongsTo(AdminModel::class, 'creator_id', 'id');
	}
	
	/**
	 * 获取用户文件列表（使用模型关联）
	 * 用于媒体选择器等用户权限场景
	 */
	public static function getUserFiles(array $params = []): array
	{
		$userId = request()->adminId;
		$query = self::with(['attachment'])
		             ->where('user_id', $userId);

		// 分类筛选
		if (isset($params['cate_id']) && $params['cate_id'] >= 0) {
			$query->where('cate_id', $params['cate_id']);
		}

		// 文件名搜索
		if (!empty($params['name']) || !empty($params['keyword'])) {
			$keyword = $params['name'] ?? $params['keyword'];
			$query->where('display_name', 'like', '%' . $keyword . '%');
		}

		// 存储类型筛选
		if (!empty($params['storage'])) {
			$query->whereHas('attachment', function($q) use ($params) {
				$q->where('storage', $params['storage']);
			});
		}

		// 文件扩展名筛选
		if (!empty($params['extension'])) {
			$query->whereHas('attachment', function($q) use ($params) {
				$q->where('extension', $params['extension']);
			});
		}

		// 媒体类型筛选（用于媒体选择器）
		if (!empty($params['media_type'])) {
			$query->whereHas('attachment', function($q) use ($params) {
				switch ($params['media_type']) {
					case 'image':
						$q->where('mime_type', 'like', 'image/%');
						break;
					case 'video':
						$q->where('mime_type', 'like', 'video/%');
						break;
					case 'audio':
						$q->where('mime_type', 'like', 'audio/%');
						break;
					case 'file':
						$q->where('mime_type', 'not like', 'image/%')
						  ->where('mime_type', 'not like', 'video/%')
						  ->where('mime_type', 'not like', 'audio/%');
						break;
				}
			});
		}

		// 排序
		$query->order('created_at', 'desc');

		// 分页
		$page  = $params['page'] ?? 1;
		$limit = $params['limit'] ?? 10;

		$result = $query->paginate([
			'page'      => $page,
			'list_rows' => $limit
		]);

		// 转换数据格式，添加物理文件信息
		$data = $result->toArray();
		$data['data'] = array_map(function($item) {
			$attachment = $item['attachment'] ?? [];
			return [
				'id' => $item['id'],
				'attachment_id' => $item['attachment_id'],
				'cate_id' => $item['cate_id'],
				'display_name' => $item['display_name'],
				'original_name' => $item['original_name'],
				'upload_source' => $item['upload_source'],
				'upload_time' => $item['created_at'],
				'created_at' => $item['created_at'],
				'updated_at' => $item['updated_at'],
				// 物理文件信息
				'name' => $attachment['name'] ?? '',
				'path' => $attachment['path'] ?? '',
				'url' => $attachment['url'] ?? '',
				'size' => $attachment['size'] ?? 0,
				'extension' => $attachment['extension'] ?? '',
				'mime_type' => $attachment['mime_type'] ?? '',
				'storage' => $attachment['storage'] ?? '',
				'ref_count' => $attachment['ref_count'] ?? 0,
			];
		}, $data['data']);

		return $data;
	}
	
	/**
	 * 检查用户是否有文件权限
	 */
	public static function checkUserFileAccess(int $fileId, int $userId): bool
	{
		$result = self::where('id', $fileId)
		              ->where('user_id', $userId)
		              ->find();
		
		return !is_null($result);
	}
	
	/**
	 * 创建用户文件关联
	 */
	public static function createUserFileAssociation(array $data): self
	{
		$model = new self();
		
		$res = $model->saveByCreate([
			'attachment_id' => $data['attachment_id'],
			'user_id'       => $data['user_id'],
			'cate_id'       => $data['cate_id'] ?? 0,
			'display_name'  => $data['display_name'],
			'original_name' => $data['original_name'],
			'upload_ip'     => $data['upload_ip'] ?? request()->ip(),
			'upload_source' => $data['upload_source'] ?? 'web',
		]);
		
		return $model;
	}
	
	/**
	 * 获取用户文件统计（使用模型关联）
	 */
	public static function getUserFileStats(): array
	{
		$userId = request()->adminId;

		// 获取用户文件总数和总大小
		$userFiles = self::with(['attachment'])
		                 ->where('user_id', $userId)
		                 ->select();

		$totalFiles = $userFiles->count();
		$totalSize = $userFiles->sum(function($item) {
			return $item->attachment->size ?? 0;
		});

		// 按分类统计
		$categoryStats = [];
		$categoryGroups = $userFiles->groupBy('cate_id');
		foreach ($categoryGroups as $cateId => $files) {
			$categoryStats[] = [
				'cate_id' => $cateId,
				'file_count' => $files->count(),
				'total_size' => $files->sum(function($item) {
					return $item->attachment->size ?? 0;
				})
			];
		}

		// 按扩展名统计
		$extensionStats = [];
		$extensionGroups = $userFiles->groupBy(function($item) {
			return $item->attachment->extension ?? 'unknown';
		});
		foreach ($extensionGroups as $extension => $files) {
			$extensionStats[] = [
				'extension' => $extension,
				'file_count' => $files->count(),
				'total_size' => $files->sum(function($item) {
					return $item->attachment->size ?? 0;
				})
			];
		}

		return [
			'total_files'          => $totalFiles,
			'total_size'           => $totalSize,
			'total_size_formatted' => BitFormat::formatSizeAuto($totalSize),
			'by_category'          => $categoryStats,
			'by_extension'         => $extensionStats,
		];
	}

	/**
	 * 获取所有文件列表（管理员专用）
	 * 用于管理页面显示所有用户的文件
	 */
	public static function getAllFiles(array $params = []): array
	{
		$query = self::with(['attachment', 'user', 'creator']);

		// 分类筛选
		if (isset($params['cate_id']) && $params['cate_id'] >= 0) {
			$query->where('cate_id', $params['cate_id']);
		}

		// 未分类筛选
		if (!empty($params['is_uncategorized'])) {
			$query->where('cate_id', 0);
		}

		// 文件名搜索
		if (!empty($params['name']) || !empty($params['keyword'])) {
			$keyword = $params['name'] ?? $params['keyword'];
			$query->where('display_name', 'like', '%' . $keyword . '%');
		}

		// 存储类型筛选
		if (!empty($params['storage'])) {
			$query->whereHas('attachment', function($q) use ($params) {
				$q->where('storage', $params['storage']);
			});
		}

		// 文件扩展名筛选
		if (!empty($params['extension'])) {
			$query->whereHas('attachment', function($q) use ($params) {
				$q->where('extension', $params['extension']);
			});
		}

		// 媒体类型筛选
		if (!empty($params['media_type'])) {
			$query->whereHas('attachment', function($q) use ($params) {
				switch ($params['media_type']) {
					case 'image':
						$q->where('mime_type', 'like', 'image/%');
						break;
					case 'video':
						$q->where('mime_type', 'like', 'video/%');
						break;
					case 'audio':
						$q->where('mime_type', 'like', 'audio/%');
						break;
					case 'file':
						$q->where('mime_type', 'not like', 'image/%')
						  ->where('mime_type', 'not like', 'video/%')
						  ->where('mime_type', 'not like', 'audio/%');
						break;
				}
			});
		}

		// 排序
		$query->order('created_at', 'desc');

		// 分页
		$page  = $params['page'] ?? 1;
		$limit = $params['limit'] ?? 10;

		$result = $query->paginate([
			'page'      => $page,
			'list_rows' => $limit
		]);

		// 转换数据格式，添加物理文件信息和用户信息
		$data = $result->toArray();
		$data['data'] = array_map(function($item) {
			$attachment = $item['attachment'] ?? [];
			$user = $item['user'] ?? [];
			$creator = $item['creator'] ?? [];

			return [
				'id' => $item['id'],
				'attachment_id' => $item['attachment_id'],
				'cate_id' => $item['cate_id'],
				'display_name' => $item['display_name'],
				'original_name' => $item['original_name'],
				'upload_source' => $item['upload_source'],
				'upload_ip' => $item['upload_ip'] ?? '',
				'upload_time' => $item['created_at'],
				// 物理文件信息
				'name' => $attachment['name'] ?? '',
				'path' => $attachment['path'] ?? '',
				'url' => $attachment['url'] ?? '',
				'size' => $attachment['size'] ?? 0,
				'extension' => $attachment['extension'] ?? '',
				'mime_type' => $attachment['mime_type'] ?? '',
				'storage' => $attachment['storage'] ?? '',
				'ref_count' => $attachment['ref_count'] ?? 0,
				// 用户信息
				'user_id' => $item['user_id'],
				'user_name' => $user['real_name'] ?? $user['username'] ?? '',
				'creator_id' => $item['creator_id'],
				'creator_name' => $creator['real_name'] ?? $creator['username'] ?? '',
			];
		}, $data['data']);

		return $data;
	}

	/**
	 * 获取所有文件统计（管理员专用）
	 */
	public static function getAllFileStats(): array
	{
		// 获取所有文件
		$allFiles = self::with(['attachment'])->select();

		$totalFiles = $allFiles->count();
		$totalSize = $allFiles->sum(function($item) {
			return $item->attachment->size ?? 0;
		});

		// 按分类统计
		$categoryStats = [];
		$categoryGroups = $allFiles->groupBy('cate_id');
		foreach ($categoryGroups as $cateId => $files) {
			$categoryStats[] = [
				'cate_id' => $cateId,
				'file_count' => $files->count(),
				'total_size' => $files->sum(function($item) {
					return $item->attachment->size ?? 0;
				})
			];
		}

		// 按扩展名统计
		$extensionStats = [];
		$extensionGroups = $allFiles->groupBy(function($item) {
			return $item->attachment->extension ?? 'unknown';
		});
		foreach ($extensionGroups as $extension => $files) {
			$extensionStats[] = [
				'extension' => $extension,
				'file_count' => $files->count(),
				'total_size' => $files->sum(function($item) {
					return $item->attachment->size ?? 0;
				})
			];
		}

		// 按存储类型统计
		$storageStats = [];
		$storageGroups = $allFiles->groupBy(function($item) {
			return $item->attachment->storage ?? 'unknown';
		});
		foreach ($storageGroups as $storage => $files) {
			$storageStats[] = [
				'storage' => $storage,
				'file_count' => $files->count(),
				'total_size' => $files->sum(function($item) {
					return $item->attachment->size ?? 0;
				})
			];
		}

		return [
			'total_files'          => $totalFiles,
			'total_size'           => $totalSize,
			'total_size_formatted' => BitFormat::formatSizeAuto($totalSize),
			'by_category'          => $categoryStats,
			'by_extension'         => $extensionStats,
			'by_storage'           => $storageStats,
		];
	}
	
	/**
	 * 批量删除用户文件
	 */
	public static function batchDeleteUserFiles(array $fileIds, int $userId): array
	{
		$successCount = 0;
		$failedCount  = 0;
		$failedIds    = [];
		$model        = new self();
		foreach ($fileIds as $fileId) {
			try {
				$userFile = $model->where('id', $fileId)
				                  ->where('user_id', $userId)
				                  ->find();
				
				if ($userFile) {
					$userFile->delete();
					$successCount++;
				}
				else {
					$failedCount++;
					$failedIds[] = $fileId;
				}
			}
			catch (\Exception $e) {
				$failedCount++;
				$failedIds[] = $fileId;
			}
		}
		
		return [
			'success_count' => $successCount,
			'failed_count'  => $failedCount,
			'failed_ids'    => $failedIds
		];
	}
}
