<template>
  <ElRow :gutter="16" class="attachment-container">
    <!-- 左侧分类树区域 -->
    <ElCol :span="6" :xs="24" :sm="8" :md="6" :lg="5" :xl="4">
      <ElCard shadow="never" class="category-card">
        <template #header>
          <div class="card-header">
            <span>附件分类</span>
            <div class="card-header-tools">
              <ElButton type="primary" size="small" @click="showCategoryDialog(null)" v-ripple
                >添加分类
              </ElButton>
            </div>
          </div>
        </template>

        <div class="category-tree-wrapper">
          <ElTree
            ref="categoryTreeRef"
            :data="categoryTreeWithAll"
            node-key="id"
            highlight-current
            :props="{ label: 'name', children: 'children' }"
            @node-click="handleCategoryNodeClick"
            :default-expanded-keys="expandedKeys"
          >
            <template #default="{ node, data }">
              <div class="custom-tree-node">
                <span class="tree-node-label">
                  <ElIcon v-if="data.id === -1" class="category-icon all-icon">
                    <Monitor />
                  </ElIcon>
                  <ElIcon v-else-if="data.id === 0" class="category-icon uncategorized-icon">
                    <Files />
                  </ElIcon>
                  <ElIcon v-else class="category-icon folder-icon">
                    <Folder />
                  </ElIcon>
                  <span class="node-text">{{ node.label }}</span>
                  <span v-if="data.children && data.children.length" class="category-count">
                    ({{ data.children.length }})
                  </span>
                </span>
                <span v-if="data.id > 0" class="tree-node-actions">
                  <ElButton type="primary" link @click.stop="showCategoryDialog(data)" title="编辑">
                    <ElIcon><Edit /></ElIcon>
                  </ElButton>
                  <ElButton
                    type="danger"
                    link
                    @click.stop="handleDeleteCategoryConfirm(data)"
                    title="删除"
                  >
                    <ElIcon><Delete /></ElIcon>
                  </ElButton>
                </span>
              </div>
            </template>
          </ElTree>
        </div>
      </ElCard>
    </ElCol>

    <!-- 右侧文件列表区域 -->
    <ElCol :span="18" :xs="24" :sm="16" :md="18" :lg="19" :xl="20">
      <ArtTableFullScreen>
        <div class="account-page" id="table-full-screen">
          <!-- 搜索栏 -->
          <ArtSearchBar
            v-model:filter="formFilters"
            :items="formItems"
            @reset="handleReset"
            @search="handleSearch"
          ></ArtSearchBar>

          <ElCard shadow="never" class="art-table-card">
            <!-- 表格头部 -->
            <ArtTableHeader :columnList="columnOptions" @refresh="handleRefresh">
              <template #left>
                <ElButton @click="showUploadDialog" type="primary" v-ripple>上传附件</ElButton>
                <ElButton
                  v-if="multipleSelection.length && currentCategoryId === 0"
                  @click="showMoveToCategoryDialog"
                  type="warning"
                  v-ripple
                >
                  批量移动
                </ElButton>
              </template>
            </ArtTableHeader>

            <!-- 表格 -->
            <ArtTable
              :loading="loading"
              :data="tableData"
              :currentPage="currentPage"
              :pageSize="pageSize"
              :total="total"
              :marginTop="10"
              @selection-change="handleSelectionChange"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            >
              <!-- 选择列 -->
              <ElTableColumn type="selection" width="55" />

              <!-- ID列 -->
              <ElTableColumn prop="id" label="ID" width="100" />

              <!-- 预览列 -->
              <ImageColumn
                label="预览"
                prop="path"
                :width="100"
                :image-width="'40px'"
                :image-height="'40px'"
                :get-src-func="(row) => row.path"
                :always-single-preview="true"
              />

              <!-- 文件名列 -->
              <ElTableColumn prop="name" label="文件名" />

              <!-- 原文件名列 -->
              <ElTableColumn prop="real_name" label="原文件名" />

              <!-- 文件类型列 -->
              <ElTableColumn prop="mime_type" label="文件类型" width="100" />

              <!-- 文件大小列 -->
              <ElTableColumn prop="size" label="文件大小" width="120" />

              <!-- 存储位置列 -->
              <ElTableColumn prop="storage" label="存储位置" width="120" />

              <!-- 上传时间列 -->
              <ElTableColumn prop="upload_time" label="上传时间" sortable width="170" />

              <!-- 操作列 -->
              <ElTableColumn label="操作" width="240" fixed="right">
                <template #default="{ row }">
                  <div class="operation-buttons">
                    <ArtButtonTable text="复制" icon="&#xe84d;" @click="handleCopy(row.path)" />
                    <ArtButtonTable
                      v-if="row.cate_id === 0"
                      text="移动"
                      type="edit"
                      @click="showMoveToCategoryDialog([row.id])"
                    />
                    <ArtButtonTable text="删除" type="delete" @click="handleDeleteConfirm(row)" />
                  </div>
                </template>
              </ElTableColumn>
            </ArtTable>
          </ElCard>
        </div>

        <!-- 上传对话框 -->
        <ElDialog v-model="dialogVisible" title="上传附件" width="500px">
          <!--          <div class="upload-container">-->
          <!--            <div class="upload-info-alert">-->
          <!--              <ElAlert v-if="currentCategoryId <= 0" type="info" :closable="false" show-icon>-->
          <!--                当前在"全部"分类下，上传的附件将归为"未分类"-->
          <!--              </ElAlert>-->
          <!--              <ElAlert v-else type="info" :closable="false" show-icon>-->
          <!--                上传的附件将归类到"{{ getCurrentCategoryName() }}"-->
          <!--              </ElAlert>-->
          <!--            </div>-->

          <!--            <el-upload-->
          <!--              class="upload-area"-->
          <!--              :action="uploadUrl"-->
          <!--              :headers="uploadHeaders"-->
          <!--              :on-success="handleUploadSuccess"-->
          <!--              :on-error="handleUploadError"-->
          <!--              :before-upload="beforeUpload"-->
          <!--              :on-exceed="handleExceed"-->
          <!--              :data="getUploadData"-->
          <!--              multiple-->
          <!--              :limit="5"-->
          <!--              drag-->
          <!--            >-->
          <!--              <el-icon class="el-icon&#45;&#45;upload">-->
          <!--                <upload-filled />-->
          <!--              </el-icon>-->
          <!--              <div class="el-upload__text"> 将文件拖到此处，或<em>点击上传</em></div>-->
          <!--              <template #tip>-->
          <!--                <div class="el-upload__tip">-->
          <!--                  支持各种类型的文件，单个文件不超过10MB，最多可上传5个文件-->
          <!--                </div>-->
          <!--              </template>-->
          <!--            </el-upload>-->
          <!--            -->
          <!--          </div>-->
          <div class="m-4">
            <FormUploader
              v-model="uploadedFiles"
              file-type="all"
              :multiple="true"
              :limit="5"
              :category-id="currentCategoryId === -1 ? 0 : currentCategoryId"
              button-text="上传附件"
              tip-text="支持各种类型的文件，单个文件不超过10MB，最多可上传5个文件"
              :use-media-selector="false"
              :drag="true"
              @success="handleFormUploaderSuccess"
              @error="handleFormUploaderError"
              @change="handleFormUploaderChange"
            />
          </div>
          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="dialogVisible = false">取消</ElButton>
            </div>
          </template>
        </ElDialog>

        <!-- 分类管理对话框 -->
        <ElDialog
          v-model="categoryDialogVisible"
          :title="categoryDialogType === 'add' ? '添加分类' : '编辑分类'"
          width="500px"
        >
          <ElForm
            ref="categoryFormRef"
            :model="categoryForm"
            :rules="categoryRules"
            label-position="top"
          >
            <ElFormItem label="父级分类" prop="parent_id">
              <ElCascader
                v-model="categoryForm.parent_id"
                :options="categoryOptions"
                :props="{
                  checkStrictly: true,
                  value: 'id',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择父级分类"
                clearable
              />
            </ElFormItem>
            <ElFormItem label="分类名称" prop="name">
              <ElInput v-model="categoryForm.name" placeholder="请输入分类名称" />
            </ElFormItem>
            <!--            <ElFormItem label="文件类型" prop="type">
                          <ElSelect v-model="categoryForm.type" placeholder="请选择文件类型">
                            <ElOption label="图片" value="image" />
                            <ElOption label="视频" value="video" />
                            <ElOption label="文档" value="document" />
                            <ElOption label="音频" value="audio" />
                            <ElOption label="其他" value="other" />
                          </ElSelect>
                        </ElFormItem>
                        <ElFormItem label="分类目录" prop="en_name">
                          <ElInput v-model="categoryForm.en_name" placeholder="请输入分类目录（英文）" />
                        </ElFormItem>-->
            <ElFormItem label="排序" prop="sort">
              <ElInputNumber v-model="categoryForm.sort" :min="0" :max="999" step-strictly />
            </ElFormItem>
          </ElForm>
          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="categoryDialogVisible = false">取消</ElButton>
              <ElButton type="primary" @click="saveCategory">确认</ElButton>
            </div>
          </template>
        </ElDialog>

        <!-- 移动到分类对话框 -->
        <ElDialog v-model="moveToCategoryDialogVisible" title="移动到分类" width="500px">
          <ElForm label-position="top">
            <ElFormItem label="选择目标分类">
              <ElCascader
                v-model="targetCategoryId"
                :options="categoryOptions"
                :props="{
                  checkStrictly: true,
                  value: 'id',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择目标分类"
              />
            </ElFormItem>
          </ElForm>
          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="moveToCategoryDialogVisible = false">取消</ElButton>
              <ElButton type="primary" @click="handleMoveToCategory">确认移动</ElButton>
            </div>
          </template>
        </ElDialog>
      </ArtTableFullScreen>
    </ElCol>
  </ElRow>
</template>

<script setup lang="ts">
  import { ElDialog, ElMessage, ElMessageBox, ElTree, ElRow, ElCol } from 'element-plus'
  import { Folder, Edit, Delete, Monitor, Files } from '@element-plus/icons-vue'
  // import { useUserStore } from '@/store/modules/user'
  import { SearchChangeParams, SearchFormItem } from '@/types/search-form'
  import { ref, reactive, onMounted, computed } from 'vue'
  import { AttachmentApi } from '@/api/attachmentApi'
  import { AttachmentCatApi } from '@/api/attachmentCatApi'
  import { ApiStatus } from '@/utils/http/status'
  import ArtButtonTable from '@/components/core/forms/ArtButtonTable.vue'
  import { ImageColumn } from '@/components/core/tables/columns'
  import { FormUploader } from '@/components/custom/FormUploader'

  // 上传相关
  // const userStore = useUserStore()
  // const { accessToken } = userStore

  // 上传对话框
  const dialogVisible = ref(false)
  const selectedUploadCategory = ref<number[]>([])
  const uploadedFiles = ref<string>('')

  // 获取上传附加数据
  /*const getUploadData = () => {
    // 如果当前在全部分类下，则使用未分类(0)
    let categoryId = currentCategoryId.value
    if (categoryId === -1) {
      categoryId = 0
    }

    return {
      cate_id: categoryId
    }
  }*/

  // 分类管理相关
  const categoryDialogVisible = ref(false)
  const categoryDialogType = ref<'add' | 'edit'>('add')
  const categoryForm = reactive({
    id: 0,
    parent_id: 0,
    type: 'image',
    name: '',
    en_name: '',
    sort: 0
  })
  const categoryRules = {
    name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
    type: [{ required: true, message: '请选择文件类型', trigger: 'change' }]
  }
  const categoryFormRef = ref()
  const categoryTreeRef = ref()

  // 分类树数据
  const categoryTree = ref<any[]>([])
  const expandedKeys = ref<number[]>([])

  // 计算属性：将分类树转换为级联选择器的选项
  const categoryOptions = computed(() => {
    return categoryTree.value.filter((item) => item.id > 0) // 过滤掉全部和未分类
  })

  // 计算属性：带有全部和未分类的分类树
  const categoryTreeWithAll = computed(() => {
    return [
      {
        id: -1,
        parent_id: -1,
        type: 'all',
        name: '全部',
        en_name: 'all',
        sort: 0
      },
      {
        id: 0,
        parent_id: 0,
        type: 'uncategorized',
        name: '未分类',
        en_name: 'uncategorized',
        sort: 0
      },
      ...categoryTree.value
    ]
  })

  // 表格数据
  const loading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)
  const currentCategoryId = ref<number>(-1) // 默认选中"全部"

  // 初始化为空数组，将通过API获取数据
  const tableData = ref<any[]>([])

  // 定义表单搜索初始值
  const initialSearchState = {
    name: '',
    created_at: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    console.log('搜索参数:', formFilters)
    currentPage.value = 1
    getTableData()
  }

  // 表单项变更处理
  const handleFormChange = (params: SearchChangeParams): void => {
    console.log('表单项变更:', params)
  }

  // 表单配置项
  const formItems: SearchFormItem[] = [
    {
      label: '文件名',
      prop: 'fileName',
      type: 'input',
      config: {
        clearable: true
      },
      onChange: handleFormChange
    }
    /*{
      label: '上传时间',
      prop: 'created_at',
      type: 'date-picker',
      config: {
        isRange: true,
        clearable: true,
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        valueFormat: 'YYYY-MM-DD HH:mm:ss'
      },
      onChange: handleFormChange
    }*/
    // {
    //   label: '创建人',
    //   prop: 'creator_id',
    //   type: 'input',
    //   config: {
    //     clearable: true
    //   },
    //   onChange: handleFormChange
    // }
  ]

  // 列配置（保留用于表格头部配置）
  const columnOptions: any[] = [
    { label: '勾选', type: 'selection' },
    { label: 'ID', prop: 'id', width: '80' },
    { label: '预览', prop: 'path', width: '80' },
    { label: '文件名', prop: 'name' },
    { label: '原文件名', prop: 'real_name' },
    { label: '文件类型', prop: 'mime_type' },
    { label: '文件大小', prop: 'size' },
    { label: '存储位置', prop: 'storage' },
    { label: '创建时间', prop: 'upload_time' },
    { label: '操作', prop: 'action', fixed: 'right', width: '190' }
  ]

  // 获取分类树数据
  const fetchCategoryTree = async () => {
    try {
      // 这里会调用您将要实现的接口
      const res = await AttachmentCatApi.list()
      if (res.code === ApiStatus.success) {
        categoryTree.value = res.data || []
        if (categoryTree.value.length > 0) {
          expandedKeys.value = [categoryTree.value[0].id]
        }
      }
    } catch (error) {
      console.error('获取分类树失败', error)
    }
  }

  // 获取表格数据
  const getTableData = async () => {
    loading.value = true
    try {
      // 调用重构后的AttachmentApi
      const res = await AttachmentApi.list({
        page: currentPage.value,
        limit: pageSize.value,
        cate_id: currentCategoryId.value === -1 ? undefined : currentCategoryId.value,
        is_uncategorized: currentCategoryId.value === 0 ? 1 : undefined,
        keyword: formFilters.name || '',
        ...formFilters
      })

      if (res.code === ApiStatus.success) {
        // 适配新的数据结构
        total.value = res.data.total || 0
        currentPage.value = res.data.current_page || 1
        pageSize.value = res.data.per_page || 10
        tableData.value = res.data.data || []

        // 处理数据格式，确保兼容性
        tableData.value = tableData.value.map((item) => ({
          ...item,
          // 兼容旧字段名
          real_name: item.original_name || item.display_name,
          name: item.display_name,
          path: item.url || item.path,
          fileType: item.mime_type,
          // 格式化文件大小
          size: formatFileSize(item.size)
        }))
      }
    } catch (error) {
      console.error('获取附件列表失败', error)
    } finally {
      loading.value = false
    }
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 分类节点点击处理
  const handleCategoryNodeClick = (data: any) => {
    currentCategoryId.value = data.id
    currentPage.value = 1
    getTableData()
  }

  // 显示分类对话框
  const showCategoryDialog = (data: any | null) => {
    if (data) {
      // 编辑模式
      categoryDialogType.value = 'edit'
      Object.assign(categoryForm, {
        id: data.id,
        parent_id: data.parent_id,
        type: data.type,
        name: data.name,
        en_name: data.en_name,
        sort: data.sort
      })
    } else {
      // 添加模式
      categoryDialogType.value = 'add'
      Object.assign(categoryForm, {
        id: 0,
        parent_id: 0,
        type: 'image',
        name: '',
        en_name: '',
        sort: 0
      })
    }
    categoryDialogVisible.value = true
  }

  // 保存分类
  const saveCategory = async () => {
    if (!categoryFormRef.value) return

    await categoryFormRef.value.validate(async (valid: boolean) => {
      if (!valid) return

      try {
        // 这里会调用您将要实现的接口
        let res
        if (categoryDialogType.value === 'add') {
          res = await AttachmentCatApi.add(categoryForm)
        } else {
          res = await AttachmentCatApi.edit(categoryForm.id, categoryForm)
        }

        if (res.code === ApiStatus.success) {
          ElMessage.success(categoryDialogType.value === 'add' ? '添加成功' : '更新成功')
          await fetchCategoryTree()
          categoryDialogVisible.value = false
        }
      } catch (error) {
        console.error('保存分类失败', error)
      }
    })
  }

  // 删除分类确认
  const handleDeleteCategoryConfirm = (data: any) => {
    console.log('data', data)
    ElMessageBox.confirm(`确定要删除分类 "${data.name}" 吗？`, '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          const res = await AttachmentCatApi.delete([data.id])
          if (res.code === ApiStatus.success) {
            ElMessage.success('删除成功')
            await fetchCategoryTree()
            // 如果当前选中的分类被删除，则重置选中状态
            if (currentCategoryId.value === data.id) {
              currentCategoryId.value = -1
              await getTableData()
            }
          }
        } catch (error) {
          console.error('删除分类失败', error)
        }
      })
      .catch(() => {
        console.log('取消删除')
      })
  }

  // 刷新处理
  const handleRefresh = () => {
    getTableData()
  }

  // 分页处理
  const handleSizeChange = (page: number) => {
    currentPage.value = page
    getTableData()
  }
  // 处理每页条数变化
  const handleCurrentChange = (val: number) => {
    currentPage.value = val
    getTableData()
  }

  // 显示上传对话框
  const showUploadDialog = () => {
    dialogVisible.value = true
    // 默认选中当前分类
    if (currentCategoryId.value) {
      selectedUploadCategory.value = [currentCategoryId.value]
    } else {
      selectedUploadCategory.value = []
    }
  }

  // FormUploader 处理函数
  const handleFormUploaderSuccess = (response: any, file: any) => {
    console.log('上传文件成功', response, file)
    ElMessage.success(`文件 ${file.name} 上传成功`)
    // 刷新列表
    getTableData()
  }

  const handleFormUploaderError = (error: any, file: any) => {
    console.log('上传文件失败', error, file)
    ElMessage.error(`文件 ${file.name} 上传失败`)
  }

  const handleFormUploaderChange = (value: string | string[], files: any[]) => {
    console.log('上传文件状态改变', value, files)
    uploadedFiles.value = Array.isArray(value) ? value.join(',') : value
  }

  // 上传成功处理
  /*const handleUploadSuccess = (response: any, file: any) => {
    console.log('response', response)
    if (response.code === ApiStatus.success) {
      ElMessage.success(`文件 ${file.name} 上传成功 ${EmojiText[200]}`)
      // 刷新列表
      getTableData()
    } else {
      ElMessage.error(`文件上传失败: ${response.message}`)
    }
  }*/

  // 上传错误处理
  /*const handleUploadError = (error: any) => {
    ElMessage.error(`文件上传失败: ${error.message || '未知错误'} ${EmojiText[500]}`)
  }*/

  // 上传前检查
  /*const beforeUpload = (file: File) => {
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      ElMessage.error(`文件大小不能超过10MB!`)
      return false
    }
    return true
  }*/

  // 超出上传数量限制
  /*const handleExceed = () => {
    ElMessage.warning('最多只能上传5个文件!')
  }*/

  // 处理复制链接
  const handleCopy = (url: string) => {
    navigator.clipboard
      .writeText(url)
      .then(() => {
        ElMessage.success('链接已复制到剪贴板')
      })
      .catch(() => {
        ElMessage.error('复制失败，请手动复制')
      })
  }

  // 处理删除确认
  const handleDeleteConfirm = (row: any) => {
    ElMessageBox.confirm(`确定要删除文件 "${row.real_name}" 吗？`, '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          const res = await AttachmentApi.delete([row.id])
          if (res.code === ApiStatus.success) {
            // 检查批量操作结果
            if (res.data && res.data.failed_count > 0) {
              ElMessage.warning(`删除完成，但有 ${res.data.failed_count} 个文件删除失败`)
            }
            await getTableData()
          }
        } catch (error) {
          console.error('删除文件失败', error)
        }
      })
      .catch(() => {
        ElMessage.info('已取消删除')
      })
  }

  // 多选相关
  const multipleSelection = ref<number[]>([])

  // 移动到分类相关
  const moveToCategoryDialogVisible = ref(false)
  const targetCategoryId = ref<number[]>([])
  const itemIdsToMove = ref<number[]>([])

  // 处理表格选择变更
  const handleSelectionChange = (selection: any[]) => {
    multipleSelection.value = selection.map((item) => item.id)
    console.log('asdf', multipleSelection.value)
  }

  // 获取当前分类名称
  /*const getCurrentCategoryName = () => {
    if (currentCategoryId.value === -1) return '全部'
    if (currentCategoryId.value === 0) return '未分类'

    const findCategory = (categories: any[], id: number): string => {
      for (const category of categories) {
        if (category.id === id) return category.name
        if (category.children?.length) {
          const name = findCategory(category.children, id)
          if (name) return name
        }
      }
      return ''
    }

    return findCategory(categoryTree.value, currentCategoryId.value as number) || '未知分类'
  }*/

  // 显示移动到分类对话框
  const showMoveToCategoryDialog = (ids?: number[] | MouseEvent) => {
    targetCategoryId.value = []
    if (Array.isArray(ids)) {
      itemIdsToMove.value = ids
    } else {
      itemIdsToMove.value = multipleSelection.value
    }
    moveToCategoryDialogVisible.value = true
  }

  // 处理移动到分类
  const handleMoveToCategory = async () => {
    if (!targetCategoryId.value || targetCategoryId.value.length === 0) {
      ElMessage.warning('请选择目标分类')
      return
    }

    const targetId = targetCategoryId.value[targetCategoryId.value.length - 1]

    try {
      // 调用重构后的批量移动API
      const res = await AttachmentApi.batchMove(itemIdsToMove.value, targetId)

      if (res.code === ApiStatus.success) {
        // 检查批量操作结果
        if (res.data && res.data.failed_count > 0) {
          ElMessage.warning(`移动完成，但有 ${res.data.failed_count} 个文件移动失败`)
        }
        // 刷新数据
        await getTableData()
        moveToCategoryDialogVisible.value = false
        multipleSelection.value = []
      }
    } catch (error) {
      console.error('移动附件失败', error)
    }
  }

  // 计算属性：获取所有图片URL用于预览
  /*const previewImages = computed(() => {
    return tableData.value.filter((item) => isImageType(item.fileType)).map((item) => item.path)
  })*/

  // 获取图片在预览数组中的索引
  /*const getImageIndex = (url: string) => {
    return previewImages.value.findIndex((item) => item === url)
  }*/

  // 生命周期钩子
  onMounted(() => {
    fetchCategoryTree()
    // 设置默认选中"全部"分类
    currentCategoryId.value = -1
    getTableData()
  })
</script>

<style lang="scss" scoped>
  .page-title {
    margin-bottom: 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    padding-bottom: 12px;

    h2 {
      font-size: 20px;
      font-weight: 500;
      color: var(--el-text-color-primary);
      margin: 0 0 4px 0;
    }

    .page-description {
      font-size: 14px;
      color: var(--el-text-color-secondary);
    }
  }

  .attachment-container {
    // 响应式布局调整
    @media (max-width: 768px) {
      .el-col {
        margin-bottom: 16px;
      }
    }

    .category-card {
      height: 100%;
      min-height: 500px; // 确保小屏幕下有足够高度

      :deep(.el-card__body) {
        padding: 0;
        height: calc(100% - 56px);
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .card-header-tools {
          display: flex;
          gap: 8px;
        }
      }

      .category-tree-wrapper {
        height: 100%;
        padding: 12px;
        overflow-y: auto;

        :deep(.el-tree) {
          .el-tree-node {
            // 覆盖默认的padding设置
            padding: 0;
            margin: 6px 0;

            // 修复树节点重复padding问题
            &__content {
              height: 40px;
              border-radius: 4px;
              transition: all 0.2s;
              padding: 0 !important;

              &:hover {
                background-color: var(--el-fill-color-light);
              }
            }

            &.is-current > .el-tree-node__content {
              background-color: var(--el-color-primary-light-9);
              box-shadow: 0 0 0 1px var(--el-color-primary-light-5);
            }
          }
        }

        .custom-tree-node {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 14px;
          padding: 0 8px;
          width: 100%;

          .tree-node-label {
            display: flex;
            align-items: center;
            flex: 1;
            overflow: hidden;

            .node-text {
              font-weight: 500;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .category-count {
              margin-left: 4px;
              color: var(--el-text-color-secondary);
              font-size: 12px;
              flex-shrink: 0;
            }

            .category-icon {
              margin-right: 8px;
              font-size: 18px;
              flex-shrink: 0;

              &.image-icon {
                color: #409eff;
              }

              &.video-icon {
                color: #67c23a;
              }

              &.document-icon {
                color: #e6a23c;
              }

              &.audio-icon {
                color: #f56c6c;
              }

              &.folder-icon {
                color: #909399;
              }

              &.all-icon {
                color: #67c23a;
              }

              &.uncategorized-icon {
                color: #f56c6c;
              }
            }
          }

          .tree-node-actions {
            visibility: hidden;
            display: flex;
            align-items: center;
            gap: 4px;
            flex-shrink: 0;
          }

          &:hover {
            .tree-node-actions {
              visibility: visible;
            }
          }
        }
      }
    }
  }

  // 文件预览样式
  .file-preview {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40px;

    .preview-image {
      width: 40px;
      height: 40px;
      object-fit: cover;
      border-radius: 4px;
      transition: transform 0.3s;

      &:hover {
        transform: scale(1.1);
      }
    }

    .file-icon {
      font-size: 24px;
      color: #909399;
    }
  }

  // 操作按钮样式
  .operation-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
  }

  // 上传容器样式
  .upload-container {
    .upload-info-alert {
      margin-bottom: 20px;
    }

    .upload-area {
      width: 100%;
      margin-top: 16px;

      :deep(.el-upload-dragger) {
        width: 100%;
        height: 200px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .el-icon--upload {
          font-size: 48px;
          margin-bottom: 10px;
          color: var(--el-color-primary);
        }

        .el-upload__text {
          color: #606266;
          font-size: 14px;
          text-align: center;

          em {
            color: var(--el-color-primary);
            font-style: normal;
          }
        }
      }

      :deep(.el-upload__tip) {
        color: #909399;
        font-size: 12px;
        line-height: 1.4;
        text-align: center;
        margin-top: 10px;
      }
    }
  }
</style>
