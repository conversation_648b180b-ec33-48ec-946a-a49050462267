<?php
declare(strict_types=1);

namespace app\system\service;

use app\system\model\AttachmentUserModel;
use app\common\exception\BusinessException;

/**
 * 附件权限验证服务
 */
class AttachmentPermissionService
{
	/**
	 * 验证用户文件访问权限
	 *
	 * @param int    $fileId    用户文件关联ID
	 * @param int    $userId    用户ID
	 * @param int    $tenantId  租户ID
	 * @param string $operation 操作类型：view, edit, delete, download
	 * @return bool
	 */
	public function validateFileAccess(int $fileId, int $userId, int $tenantId, string $operation = 'view'): bool
	{
		// 1. 系统超级管理员
		if (is_super_admin()) {
			return true;
		}
		
		// 2. 租户超级管理员（可以访问本租户所有文件）
		if (is_tenant_super_admin() && $this->isFileBelongsToTenant($fileId, $tenantId)) {
			return true;
		}
		
		// 3. 文件所有者权限
		if (AttachmentUserModel::checkUserFileAccess($fileId, $userId, $tenantId)) {
			return true;
		}
		
		// 4. 共享权限检查（预留扩展）
		if ($this->checkSharedFileAccess($fileId, $userId, $tenantId, $operation)) {
			return true;
		}
		
		return false;
	}
	
	/**
	 * 验证批量文件访问权限
	 *
	 * @param array  $fileIds   文件ID数组
	 * @param int    $userId    用户ID
	 * @param int    $tenantId  租户ID
	 * @param string $operation 操作类型
	 * @return array ['allowed' => [], 'denied' => []]
	 */
	public function validateBatchFileAccess(array $fileIds, int $userId, int $tenantId, string $operation = 'view'
	): array
	{
		$allowed = [];
		$denied  = [];
		
		foreach ($fileIds as $fileId) {
			if ($this->validateFileAccess($fileId, $userId, $tenantId, $operation)) {
				$allowed[] = $fileId;
			}
			else {
				$denied[] = $fileId;
			}
		}
		
		return [
			'allowed' => $allowed,
			'denied'  => $denied
		];
	}
	
	/**
	 * 验证文件上传权限
	 *
	 * @param int $userId   用户ID
	 * @param int $tenantId 租户ID
	 * @param int $cateId   分类ID
	 * @return bool
	 */
	public function validateUploadPermission(int $userId, int $tenantId, int $cateId = 0): bool
	{
		// 1. 系统超级管理员
		if (is_super_admin()) {
			return true;
		}
		
		// 2. 租户超级管理员
		if (is_tenant_super_admin()) {
			return true;
		}
		
		// 3. 检查用户是否有上传权限
		if (!$this->checkUserUploadPermission($userId, $tenantId)) {
			return false;
		}
		
		// 4. 检查分类权限（如果指定了分类）
		if ($cateId > 0 && !$this->checkCategoryPermission($userId, $tenantId, $cateId)) {
			return false;
		}
		
		// 5. 检查存储配额（预留扩展）
		if (!$this->checkStorageQuota($userId, $tenantId)) {
			return false;
		}
		
		return true;
	}
	
	/**
	 * 验证文件下载权限
	 *
	 * @param int $fileId   文件ID
	 * @param int $userId   用户ID
	 * @param int $tenantId 租户ID
	 * @return bool
	 */
	public function validateDownloadPermission(int $fileId, int $userId, int $tenantId): bool
	{
		return $this->validateFileAccess($fileId, $userId, $tenantId, 'download');
	}
	
	/**
	 * 检查文件是否属于指定租户
	 *
	 * @param int $fileId   文件ID
	 * @param int $tenantId 租户ID
	 * @return bool
	 */
	private function isFileBelongsToTenant(int $fileId, int $tenantId): bool
	{
		$userFile = AttachmentUserModel::where('id', $fileId)
		                               ->where('tenant_id', $tenantId)
		                               ->where('deleted_at', null)
		                               ->find();
		
		return !is_null($userFile);
	}
	
	/**
	 * 检查共享文件访问权限（预留扩展）
	 *
	 * @param int    $fileId    文件ID
	 * @param int    $userId    用户ID
	 * @param int    $tenantId  租户ID
	 * @param string $operation 操作类型
	 * @return bool
	 */
	private function checkSharedFileAccess(int $fileId, int $userId, int $tenantId, string $operation): bool
	{
		// TODO: 实现文件共享权限检查
		// 1. 检查是否有共享给该用户
		// 2. 检查共享权限类型（只读、读写等）
		// 3. 检查共享是否过期
		
		return false;
	}
	
	/**
	 * 检查用户上传权限
	 *
	 * @param int $userId   用户ID
	 * @param int $tenantId 租户ID
	 * @return bool
	 */
	private function checkUserUploadPermission(int $userId, int $tenantId): bool
	{
		// TODO: 根据角色权限检查用户是否有上传权限
		// 可以通过PermissionService检查用户是否有 'system:attachment:upload' 权限
		
		$permissionService = new PermissionService();
		$userPermissions   = $permissionService->getAdminPermissionByAdminId($userId);
		
		return in_array('system:attachment:upload', $userPermissions);
	}
	
	/**
	 * 检查分类权限
	 *
	 * @param int $userId   用户ID
	 * @param int $tenantId 租户ID
	 * @param int $cateId   分类ID
	 * @return bool
	 */
	private function checkCategoryPermission(int $userId, int $tenantId, int $cateId): bool
	{
		// TODO: 实现分类权限检查
		// 1. 检查用户是否有访问该分类的权限
		// 2. 检查分类是否属于该租户
		
		return true; // 暂时允许所有分类
	}
	
	/**
	 * 检查存储配额
	 *
	 * @param int $userId   用户ID
	 * @param int $tenantId 租户ID
	 * @return bool
	 */
	private function checkStorageQuota(int $userId, int $tenantId): bool
	{
		// TODO: 实现存储配额检查
		// 1. 获取用户/租户的存储配额
		// 2. 计算当前使用量
		// 3. 检查是否超出配额
		
		return true; // 暂时不限制配额
	}
	
	/**
	 * 记录权限验证日志
	 *
	 * @param string $operation 操作类型
	 * @param int    $fileId    文件ID
	 * @param int    $userId    用户ID
	 * @param bool   $result    验证结果
	 * @param string $reason    原因
	 */
	public function logPermissionCheck(string $operation, int $fileId, int $userId, bool $result, string $reason = ''
	): void
	{
		// TODO: 记录权限验证日志到审计表
		// 可以用于安全审计和问题排查
		
		$logData = [
			'operation'  => $operation,
			'file_id'    => $fileId,
			'user_id'    => $userId,
			'result'     => $result
				? 'allowed'
				: 'denied',
			'reason'     => $reason,
			'ip'         => request()->ip(),
			'user_agent' => request()->header('User-Agent'),
			'created_at' => date('Y-m-d H:i:s')
		];
		
		// 记录到日志文件或数据库
		trace('Attachment Permission Check: ' . json_encode($logData), 'info');
	}
	
	/**
	 * 获取用户文件权限摘要
	 *
	 * @param int $userId   用户ID
	 * @param int $tenantId 租户ID
	 * @return array
	 */
	public function getUserFilePermissionSummary(int $userId, int $tenantId): array
	{
		return [
			'can_upload'    => $this->validateUploadPermission($userId, $tenantId),
			'can_download'  => true,
			// 基础权限
			'can_delete'    => true,
			// 基础权限
			'can_share'     => false,
			// 预留功能
			'is_admin'      => is_super_admin() || is_tenant_super_admin(),
			'storage_quota' => [
				'used'       => 0,
				// TODO: 计算已使用空间
				'total'      => -1,
				// TODO: 获取配额限制，-1表示无限制
				'percentage' => 0
				// TODO: 计算使用百分比
			]
		];
	}
}
