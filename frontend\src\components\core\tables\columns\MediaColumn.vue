<!-- 媒体列组件 - 用于音频、视频的展示和播放 -->
<template>
  <el-table-column :label="label" :prop="prop" :width="width" :align="align || 'center'">
    <template #default="scope">
      <div v-if="hasMedia(scope.row)" class="media-link" @click="previewMedia(scope.row)">
        <!-- 如果是视频且有封面图，显示封面图预览 -->
        <div v-if="getMediaType(scope.row) === MEDIA_TYPES.VIDEO" class="video-thumbnail">
          <!-- 有自定义封面图时显示图片 -->
          <img
            v-if="getVideoCover(scope.row)"
            :src="getVideoCover(scope.row)"
            :alt="getMediaName(scope.row)"
            class="cover-image"
          />
          <!-- 无封面图时使用Element Plus图标 -->
          <div v-else class="default-cover">
            <el-icon>
              <VideoCamera />
            </el-icon>
          </div>

          <div class="play-icon-overlay">
            <el-icon>
              <VideoPlay />
            </el-icon>
          </div>
        </div>
        <!-- 音频使用图标 -->
        <template v-else>
          <el-icon class="media-icon" :class="getMediaIconClass(scope.row)">
            <component :is="getMediaIcon(scope.row)"></component>
          </el-icon>
          <span>{{ getMediaName(scope.row) }}</span>
        </template>
      </div>
      <div v-else class="no-media">
        <span>{{ emptyText }}</span>
      </div>
    </template>
  </el-table-column>

  <!-- 预览对话框 -->
  <el-dialog
    v-model="previewVisible"
    :title="previewTitle"
    width="auto"
    :destroy-on-close="true"
    :before-close="closePreview"
  >
    <div class="media-preview-container" v-loading="previewLoading">
      <!-- 视频播放器 -->
      <video
        v-if="currentMediaType === MEDIA_TYPES.VIDEO && previewUrl"
        class="media-player video-player"
        controls
        autoplay
        controlsList="nodownload"
        :poster="currentVideoCover"
      >
        <source :src="previewUrl" :type="getMediaMimeType(previewUrl)" />
        您的浏览器不支持视频播放
      </video>

      <!-- 音频播放器 -->
      <audio
        v-else-if="currentMediaType === MEDIA_TYPES.AUDIO && previewUrl"
        class="media-player audio-player"
        controls
        autoplay
        controlsList="nodownload"
      >
        <source :src="previewUrl" :type="getMediaMimeType(previewUrl)" />
        您的浏览器不支持音频播放
      </audio>

      <!-- 无法预览 -->
      <div v-else class="media-preview-unsupported">
        <el-icon size="48">
          <WarningFilled />
        </el-icon>
        <p
          >当前媒体格式无法预览，请
          <a :href="downloadUrl" target="_blank" rel="noopener noreferrer">下载</a> 后查看</p
        >
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closePreview">关闭</el-button>
        <el-button type="primary" v-if="downloadUrl" @click="downloadMedia"> 下载 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, PropType } from 'vue'
  import { VideoPlay, VideoCamera, WarningFilled } from '@element-plus/icons-vue'

  // 媒体类型枚举
  const MEDIA_TYPES = {
    VIDEO: 'video',
    AUDIO: 'audio',
    UNKNOWN: 'unknown'
  }

  // 文件类型与扩展名的映射
  const FILE_EXTENSIONS = {
    // 视频格式
    mp4: MEDIA_TYPES.VIDEO,
    webm: MEDIA_TYPES.VIDEO,
    ogg: MEDIA_TYPES.VIDEO,
    mov: MEDIA_TYPES.VIDEO,
    avi: MEDIA_TYPES.VIDEO,
    flv: MEDIA_TYPES.VIDEO,
    wmv: MEDIA_TYPES.VIDEO,
    mkv: MEDIA_TYPES.VIDEO,
    // 音频格式
    mp3: MEDIA_TYPES.AUDIO,
    wav: MEDIA_TYPES.AUDIO,
    ogg_audio: MEDIA_TYPES.AUDIO,
    aac: MEDIA_TYPES.AUDIO,
    flac: MEDIA_TYPES.AUDIO,
    m4a: MEDIA_TYPES.AUDIO
  }

  // MIME类型映射
  const MIME_TYPES = {
    // 视频
    mp4: 'video/mp4',
    webm: 'video/webm',
    ogg: 'video/ogg',
    mov: 'video/quicktime',
    avi: 'video/x-msvideo',
    flv: 'video/x-flv',
    wmv: 'video/x-ms-wmv',
    mkv: 'video/x-matroska',
    // 音频
    mp3: 'audio/mpeg',
    wav: 'audio/wav',
    ogg_audio: 'audio/ogg',
    aac: 'audio/aac',
    flac: 'audio/flac',
    m4a: 'audio/mp4'
  }

  const props = defineProps({
    // 列标签
    label: { type: String, required: true },
    // 数据属性名
    prop: { type: String, required: true },
    // 列宽度
    width: { type: [String, Number] },
    // 对齐方式
    align: { type: String, default: 'center' },
    // 媒体URL获取函数
    getMediaUrl: {
      type: Function as PropType<(row: any) => string>,
      default: null
    },
    // 媒体名称获取函数
    getMediaNameFunc: {
      type: Function as PropType<(row: any) => string>,
      default: null
    },
    // 媒体类型获取函数
    getMediaTypeFunc: {
      type: Function as PropType<(row: any) => string>,
      default: null
    },
    // 视频封面图获取函数
    getVideoCoverFunc: {
      type: Function as PropType<(row: any) => string>,
      default: null
    },
    // 是否自动生成封面图（如果后端未提供）
    autoGenerateCover: {
      type: Boolean,
      default: false
    },
    // 视频播放器配置
    videoPlayerOptions: {
      type: Object,
      default: () => ({
        autoplay: true,
        controls: true,
        preload: 'auto'
      })
    },
    // 音频播放器配置
    audioPlayerOptions: {
      type: Object,
      default: () => ({
        autoplay: true,
        controls: true,
        preload: 'auto'
      })
    },
    // 空值显示文本
    emptyText: { type: String, default: '无媒体' }
  })

  // 预览状态
  const previewVisible = ref(false)
  const previewLoading = ref(false)
  const previewTitle = ref('')
  const previewUrl = ref('')
  const downloadUrl = ref('')
  const currentMediaType = ref('')
  const currentVideoCover = ref('')

  // 判断是否有媒体
  const hasMedia = (row: any): boolean => {
    const url = getMediaUrl(row)
    return !!url
  }

  // 获取媒体URL
  const getMediaUrl = (row: any): string => {
    if (props.getMediaUrl) {
      return props.getMediaUrl(row)
    }
    return row[props.prop] || ''
  }

  // 获取媒体名称
  const getMediaName = (row: any): string => {
    if (props.getMediaNameFunc) {
      return props.getMediaNameFunc(row)
    }

    // 尝试从URL中提取文件名
    const url = getMediaUrl(row)
    if (url) {
      const parts = url.split('/')
      return parts[parts.length - 1]
    }

    return '媒体文件'
  }

  // 获取媒体类型
  const getMediaType = (row: any): string => {
    // 优先使用自定义函数
    if (props.getMediaTypeFunc) {
      return props.getMediaTypeFunc(row)
    }

    // 从文件名或URL中提取扩展名并判断类型
    const fileName = getMediaName(row)
    const extension = fileName.split('.').pop()?.toLowerCase() || ''

    // 处理特殊情况：ogg既可以是视频也可以是音频
    if (extension === 'ogg') {
      // 这里简单处理，实际可能需要更复杂的逻辑或让用户指定
      return MEDIA_TYPES.VIDEO // 默认按视频处理
    }

    return (FILE_EXTENSIONS as any)[extension] || MEDIA_TYPES.UNKNOWN
  }

  // 获取视频封面图
  const getVideoCover = (row: any): string => {
    // 优先使用自定义函数获取封面图
    if (props.getVideoCoverFunc) {
      return props.getVideoCoverFunc(row)
    }

    // 检查是否有专门的封面图字段
    if (row.coverImage) {
      return row.coverImage
    }

    if (row.thumbnail) {
      return row.thumbnail
    }

    if (row.poster) {
      return row.poster
    }

    // 不再返回默认封面图，而是返回空字符串，使用图标显示
    // 默认不自动生成封面图，除非明确指定
    if (props.autoGenerateCover && row.defaultCoverUrl) {
      return row.defaultCoverUrl
    }

    return ''
  }

  // 获取媒体MIME类型
  const getMediaMimeType = (url: string): string => {
    const extension = url.split('.').pop()?.toLowerCase() || ''

    // 处理特殊情况：ogg既可以是视频也可以是音频
    if (extension === 'ogg') {
      return currentMediaType.value === MEDIA_TYPES.AUDIO ? 'audio/ogg' : 'video/ogg'
    }

    return MIME_TYPES[extension as keyof typeof MIME_TYPES] || ''
  }

  // 根据媒体类型获取对应图标
  const getMediaIcon = (row: any): string => {
    const mediaType = getMediaType(row)

    switch (mediaType) {
      case MEDIA_TYPES.VIDEO:
        return 'VideoPlay'
      case MEDIA_TYPES.AUDIO:
        return 'MusicNote'
      default:
        return 'Document'
    }
  }

  // 获取媒体图标样式类名
  const getMediaIconClass = (row: any): string => {
    const mediaType = getMediaType(row)
    return `media-icon-${mediaType}`
  }

  // 预览媒体
  const previewMedia = (row: any) => {
    const url = getMediaUrl(row)
    if (!url) return

    previewLoading.value = true
    previewTitle.value = getMediaName(row)
    previewUrl.value = url
    downloadUrl.value = url
    currentMediaType.value = getMediaType(row)
    currentVideoCover.value = getVideoCover(row)
    previewVisible.value = true

    // 模拟加载完成
    setTimeout(() => {
      previewLoading.value = false
    }, 1000)
  }

  // 关闭预览
  const closePreview = () => {
    previewVisible.value = false
    previewUrl.value = ''
    currentMediaType.value = ''
    currentVideoCover.value = ''
  }

  // 下载媒体
  const downloadMedia = () => {
    if (downloadUrl.value) {
      window.open(downloadUrl.value, '_blank')
    }
  }
</script>

<style scoped>
  .media-link {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.2s;
  }

  .media-link:hover {
    background-color: var(--el-fill-color-light);
    color: var(--el-color-primary);
  }

  .media-icon {
    margin-right: 5px;
    font-size: 16px;
  }

  /* 媒体类型图标颜色 */
  .media-icon-video {
    color: #e74c3c;
  }

  .media-icon-audio {
    color: #3498db;
  }

  .no-media {
    color: var(--el-text-color-secondary);
    font-size: 14px;
  }

  /* 视频缩略图样式 */
  .video-thumbnail {
    position: relative;
    width: 60px;
    height: 40px;
    margin-right: 8px;
    border-radius: 4px;
    overflow: hidden;
    background-color: #000;
  }

  .cover-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* 默认封面样式 */
  .default-cover {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #34495e, #2c3e50);
    color: #ecf0f1;
    font-size: 20px;
  }

  .play-icon-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.3);
    color: #fff;
    font-size: 18px;
    transition: background-color 0.2s;
  }

  .video-thumbnail:hover .play-icon-overlay {
    background-color: rgba(0, 0, 0, 0.5);
  }

  .media-preview-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    max-height: 70vh;
    overflow: hidden;
    position: relative;
  }

  .media-player {
    max-width: 100%;
    max-height: 70vh;
  }

  .video-player {
    width: 100%;
    background-color: #000;
  }

  .audio-player {
    width: 100%;
    min-width: 300px;
  }

  .media-preview-unsupported {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--el-text-color-secondary);
    padding: 30px;
  }

  .media-preview-unsupported p {
    margin-top: 15px;
  }

  .media-preview-unsupported a {
    color: var(--el-color-primary);
    text-decoration: none;
  }
</style>
