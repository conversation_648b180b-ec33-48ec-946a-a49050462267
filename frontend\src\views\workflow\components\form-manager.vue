<template>
  <div>
    <!-- 调试信息 -->
    <div v-if="false" style="margin-bottom: 10px; color: red">
      当前工作流ID: {{ formState.typeId }} 表单类型: {{ formState.type }}
    </div>

    <!-- 动态加载业务表单组件 -->
    <component
      :is="currentFormComponent"
      v-if="currentFormComponent"
      v-model="formState.visible"
      :formId="formState.currentFormId"
      :definitionId="formState.typeId"
      @submit="handleSubmitForm"
      @save="handleSaveForm"
      @success="handleFormSuccess"
      @cancel="handleFormCancel"
      ref="formComponentRef"
    />
  </div>
</template>

<script setup lang="ts">
  import { ApplicationApi } from '@/api/workflow/ApplicationApi'
  import { ElMessage } from 'element-plus'
  import { ApiStatus } from '@/utils/http/status'

  // 类型定义
  interface FormData {
    id?: number | string
    workflow_instance_id?: number | string
    business_id?: number | string
    [key: string]: any
  }

  interface FormState {
    type: string
    visible: boolean
    loading: boolean
    typeId: number | string
    currentFormId: number | string
    preloadedEditData: FormData | null
  }

  interface FormComponentRef {
    showForm?: (id?: number | string) => void
    setFormData?: (data: FormData) => void
    updateFormData?: (data: Partial<FormData>) => void
    submitting?: boolean
    saving?: boolean
  }

  interface FormSuccessData {
    type: 'submit' | 'save'
    data: any
  }

  // Props 类型定义
  interface Props {
    type: string
    formId: number | string
    modelValue: boolean
    workflowTypeId: number | string
  }

  // 聚合表单状态管理
  const formState = reactive<FormState>({
    type: '',
    visible: false,
    loading: false,
    typeId: 0,
    currentFormId: 0,
    preloadedEditData: null
  })

  // 表单组件引用
  const formComponentRef = ref<FormComponentRef>()

  // 定义Props
  const props = withDefaults(defineProps<Props>(), {
    type: '',
    formId: 0,
    modelValue: false,
    workflowTypeId: 0
  })

  // 定义事件
  const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    success: [data: FormSuccessData & { type: string }]
    cancel: []
  }>()

  // 计算属性
  const isEditMode = computed(() => !!formState.currentFormId || !!formState.preloadedEditData)
  const shouldShowForm = computed(() => formState.visible && !formState.loading)

  // 动态加载表单组件
  const currentFormComponent = computed(() => {
    if (!formState.type) return null

    try {
      return markRaw(
        defineAsyncComponent({
          loader: () =>
            import(`../components/business-forms/${formState.type}-form.vue`).catch((error) => {
              console.error(`加载表单组件失败: ${formState.type}-form.vue`, error)
              return null
            }),
          delay: 300,
          timeout: 5000,
          errorComponent: null,
          loadingComponent: null
        })
      )
    } catch (error) {
      console.error(`加载表单组件异常: ${formState.type}-form.vue`, error)
      return null
    }
  })

  // 优化后的 watch - 统一处理 props 变化
  watchEffect(() => {
    // 同步 props 到 formState
    formState.type = props.type
    formState.typeId = props.workflowTypeId
    formState.currentFormId = props.formId
    formState.visible = props.modelValue

    // 调试日志
    if (props.workflowTypeId) {
      console.log('FormManager 接收到 workflowTypeId:', props.workflowTypeId)
    }
  })

  // 处理表单显示逻辑 - 使用 watchPostEffect 确保 DOM 更新后执行
  watchPostEffect(() => {
    if (formState.visible && formComponentRef.value) {
      // 检查是否需要显示表单
      if (!isEditMode.value) {
        // 发起模式：没有预加载数据且没有当前表单ID
        console.log('FormManager: 发起表单，调用 showForm()')
        formComponentRef.value.showForm?.()
      } else {
        // 编辑模式：有预加载数据或已有表单ID
        console.log('FormManager: 编辑模式或已有数据，跳过 showForm 调用')
      }
    }
  })

  // 处理表单关闭时的清理
  watch(
    () => formState.visible,
    (newVisible, oldVisible) => {
      // 同步到父组件
      if (newVisible !== props.modelValue) {
        emit('update:modelValue', newVisible)
      }

      // 表单关闭时清理状态
      if (oldVisible && !newVisible) {
        formState.preloadedEditData = null
        formState.currentFormId = 0
        console.log('FormManager: 表单关闭，清理状态')
      }
    }
  )

  // 显示表单
  const showForm = async (formIdParam?: number | string): Promise<void> => {
    // 优先使用参数中的ID，其次使用props中的ID
    const idToUse = formIdParam || props.formId || formState.currentFormId
    console.log('FormManager.showForm - ID:', idToUse, '类型:', formState.type)

    // 检查是否有预加载的编辑数据，如果有则不重复请求
    if (formState.preloadedEditData) {
      console.log('FormManager: 使用预加载数据，跳过API请求')
      return
    }

    // 如果有表单ID，则先获取表单数据
    if (idToUse) {
      try {
        formState.loading = true
        console.log('FormManager: 请求详情数据，ID:', idToUse)
        const res = await ApplicationApi.detail(Number(idToUse))
        console.log('FormManager获取到的表单数据:', res.data)

        // HTTP拦截器已确保请求成功，直接处理数据
        if (res.data) {
          // 更新表单类型和工作流ID
          formState.type = res.data.business_code || formState.type
          formState.typeId = res.data.definition_id || formState.typeId

          // 更新当前表单ID
          formState.currentFormId = Number(idToUse)

          // 使用 nextTick 替代 setTimeout，确保组件挂载完成
          await nextTick()
          if (formComponentRef.value?.showForm) {
            console.log('调用业务表单showForm方法，ID:', formState.currentFormId)
            formComponentRef.value.showForm(formState.currentFormId)
          } else {
            console.error('表单组件未挂载或没有showForm方法')
          }
        }
      } catch (error) {
        console.error('加载表单数据失败', error)
        // HTTP拦截器已处理错误提示
      } finally {
        formState.loading = false
      }
    } else if (formComponentRef.value?.showForm) {
      // 如果没有表单ID，则直接显示空表单
      console.log('显示空表单')
      formComponentRef.value.showForm()
    }
  }

  // 设置编辑数据 - 由父组件调用
  const setEditData = async (data: FormData & { formData?: any }): Promise<void> => {
    console.log('FormManager收到编辑数据:', data)

    // 立即设置当前表单ID，防止watch中的重复调用
    formState.currentFormId = data.id || 0
    formState.preloadedEditData = data

    // 准备传递给业务表单组件的数据
    const formDataWithId: FormData = {
      ...data.formData,
      id: data.id
    }

    // 尝试直接传递给业务表单组件
    if (formComponentRef.value?.setFormData) {
      console.log('直接传递表单数据到业务组件')
      formComponentRef.value.setFormData(formDataWithId)
      return
    }

    // 如果组件未挂载，等待下一个 tick
    console.warn('表单组件未挂载，等待组件挂载')
    await nextTick()

    if (formComponentRef.value?.setFormData) {
      formComponentRef.value.setFormData(formDataWithId)
      formState.preloadedEditData = null
    } else {
      // 最后的兜底处理，使用 watchPostEffect 监听组件挂载
      console.warn('组件仍未挂载，将在组件挂载后自动设置数据')
    }
  }

  // 统一处理表单提交
  const handleSubmitForm = async (formData: FormData): Promise<void> => {
    console.log('FormManager.handleSubmitForm 被调用，数据:', formData)
    if (formState.loading) return

    formState.loading = true

    try {
      const res = await ApplicationApi.submit({
        business_code: formState.type,
        definition_id: formState.typeId,
        business_data: formData,
        id: formData.id || 0
      })

      if (res.code === ApiStatus.success) {
        ElMessage.success(res.message || '申请提交成功')
        handleFormSuccess({
          type: 'submit',
          data: res.data
        })
      }
    } catch (error) {
      console.error('申请提交失败:', error)
      // HTTP拦截器已处理错误提示
    } finally {
      formState.loading = false
      // 重置子组件的loading状态
      if (formComponentRef.value?.submitting !== undefined) {
        formComponentRef.value.submitting = false
      }
    }
  }

  // 统一处理表单保存为草稿
  const handleSaveForm = async (formData: FormData): Promise<void> => {
    console.log('FormManager.handleSaveForm 被调用，数据:', formData)
    if (formState.loading) return

    formState.loading = true

    try {
      const res = await ApplicationApi.save({
        business_code: formState.type,
        definition_id: formState.typeId,
        business_data: formData,
        id: formData.id || 0
      })

      if (res.code === ApiStatus.success) {
        ElMessage.success(res.message || '草稿保存成功')

        // 更新表单数据（特别是ID）
        if (formComponentRef.value && res.data) {
          // 如果是新增，需要更新表单的ID
          if (!formData.id && res.data.instance_id) {
            formState.currentFormId = res.data.instance_id
            formComponentRef.value.updateFormData?.({
              id: res.data.instance_id,
              workflow_instance_id: res.data.instance_id,
              business_id: res.data.business_id
            })
          }
        }

        handleFormSuccess({
          type: 'save',
          data: res.data
        })
      }
    } catch (error) {
      console.error('草稿保存失败:', error)
      // HTTP拦截器已处理错误提示
    } finally {
      formState.loading = false
      // 重置子组件的loading状态
      if (formComponentRef.value?.saving !== undefined) {
        formComponentRef.value.saving = false
      }
    }
  }

  // 表单提交成功
  const handleFormSuccess = (data: FormSuccessData): void => {
    emit('success', {
      type: formState.type,
      ...data
    })
  }

  // 表单取消
  const handleFormCancel = (): void => {
    emit('cancel')
  }

  // 暴露方法给父组件
  defineExpose({
    showForm,
    setEditData
  })
</script>

<!--
统一业务表单API适配说明：

1. 所有业务表单组件（如leave-form.vue, travel-form.vue等）直接调用ApplicationApi中的方法：
   - 提交表单：ApplicationApi.submit(formData)
   - 保存草稿：ApplicationApi.save(formData)
   - 获取表单数据：ApplicationApi.detail(id)

2. 提交和保存时，需要确保表单数据中包含业务类型标识，如：
   formData = {
     ...表单数据,
     form_type: 'leave',  // 表单类型标识
     business_code: 'leave'  // 业务代码，与表单类型一致
   }

3. 表单组件与父组件的通信：
   - 表单组件提交/保存成功后，调用emit('success', {type: 'xxx', data: result})
   - 表单取消时，调用emit('cancel')

4. 表单数据加载逻辑：
   - 编辑模式时，通过ApplicationApi.detail(formId)获取数据
   - 发起模式时，初始化空表单

注意：各业务表单组件应当保持一致的接口和行为，以便统一管理和维护。
-->
