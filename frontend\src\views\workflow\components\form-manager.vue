<template>
  <div>
    <!-- 调试信息 -->
    <div v-if="false" style="margin-bottom: 10px; color: red">
      当前工作流ID: {{ formState.typeId }} 表单类型: {{ formState.type }}
    </div>

    <!-- 性能监控面板 (开发环境) -->
    <div
      v-if="import.meta.env.DEV && false"
      class="performance-stats dev-mode"
    >
      <div class="stat-item">缓存命中: {{ performanceMetrics.cacheHits }}</div>
      <div class="stat-item">缓存未命中: {{ performanceMetrics.cacheMisses }}</div>
      <div class="stat-item">命中率: {{ getPerformanceStats().cacheHitRate }}</div>
      <div class="stat-item">平均加载时间: {{ getPerformanceStats().averageLoadTime }}</div>
      <div class="stat-item">当前组件: {{ formState.type || '无' }}</div>
    </div>

    <!-- 动态加载业务表单组件 -->
    <component
      :is="currentFormComponent"
      v-if="currentFormComponent"
      v-model="formState.visible"
      :formId="formState.currentFormId"
      :definitionId="formState.typeId"
      @submit="handleSubmitForm"
      @save="handleSaveForm"
      @success="handleFormSuccess"
      @cancel="handleFormCancel"
      ref="formComponentRef"
    />
  </div>
</template>

<script setup lang="ts">
  import { ApplicationApi } from '@/api/workflow/ApplicationApi'
  import { ElMessage } from 'element-plus'
  import { ApiStatus } from '@/utils/http/status'

  // 类型定义
  interface FormData {
    id?: number | string
    workflow_instance_id?: number | string
    business_id?: number | string

    [key: string]: any
  }

  interface FormState {
    type: string
    visible: boolean
    loading: boolean
    typeId: number | string
    currentFormId: number | string
    preloadedEditData: FormData | null
  }

  interface FormComponentRef {
    showForm?: (id?: number | string) => void
    setFormData?: (data: FormData) => void
    updateFormData?: (data: Partial<FormData>) => void
    submitting?: boolean
    saving?: boolean
  }

  interface FormSuccessData {
    type: 'submit' | 'save'
    data: any
  }

  // Props 类型定义
  interface Props {
    type: string
    formId: number | string
    modelValue: boolean
    workflowTypeId: number | string
  }

  // 聚合表单状态管理
  const formState = reactive<FormState>({
    type: '',
    visible: false,
    loading: false,
    typeId: 0,
    currentFormId: 0,
    preloadedEditData: null
  })

  // 表单组件引用
  const formComponentRef = ref<FormComponentRef>()

  // 定义Props
  const props = withDefaults(defineProps<Props>(), {
    type: '',
    formId: 0,
    modelValue: false,
    workflowTypeId: 0
  })

  // 定义事件
  const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    success: [data: FormSuccessData & { type: string }]
    cancel: []
  }>()

  // 计算属性
  const isEditMode = computed(() => !!formState.currentFormId || !!formState.preloadedEditData)
  const shouldShowForm = computed(() => formState.visible && !formState.loading)

  // 组件缓存 - 使用 Map 存储已加载的组件
  const componentCache = new Map<string, any>()

  // 组件加载状态跟踪
  const componentLoadingStates = reactive<Record<string, boolean>>({})

  // 性能监控
  const performanceMetrics = reactive({
    loadTimes: {} as Record<string, number>,
    cacheHits: 0,
    cacheMisses: 0
  })

  // 创建加载组件的 Loading 组件
  const LoadingComponent = defineComponent({
    template: `
      <div class="form-loading-container">
        <el-skeleton :rows="5" animated />
        <div class="loading-text">正在加载表单组件...</div>
      </div>
    `
  })

  // 创建错误组件
  const ErrorComponent = defineComponent({
    props: {
      error: Object,
      retry: Function
    },
    template: `
      <div class="form-error-container">
        <el-alert
          title="组件加载失败"
          type="error"
          :description="error?.message || '未知错误'"
          show-icon
        />
        <el-button
          type="primary"
          size="small"
          @click="retry"
          style="margin-top: 10px"
        >
          重新加载
        </el-button>
      </div>
    `
  })

  // 优化的组件加载器
  const loadFormComponent = async (type: string) => {
    const startTime = performance.now()

    try {
      // 检查缓存
      if (componentCache.has(type)) {
        performanceMetrics.cacheHits++
        console.log(`📦 组件缓存命中: ${type}`)
        return componentCache.get(type)
      }

      performanceMetrics.cacheMisses++
      componentLoadingStates[type] = true

      console.log(`🔄 开始加载组件: ${type}`)

      // 动态导入组件
      const componentModule = await import(`../components/business-forms/${type}-form.vue`)
      const component = componentModule.default

      // 缓存组件
      componentCache.set(type, component)

      // 记录加载时间
      const loadTime = performance.now() - startTime
      performanceMetrics.loadTimes[type] = loadTime

      console.log(`✅ 组件加载成功: ${type}, 耗时: ${loadTime.toFixed(2)}ms`)

      return component
    } catch (error) {
      console.error(`❌ 组件加载失败: ${type}`, error)
      throw error
    } finally {
      componentLoadingStates[type] = false
    }
  }

  // 预加载常用组件
  const preloadCommonComponents = async () => {
    const commonTypes = ['leave', 'travel', 'expense', 'purchase'] // 常用的表单类型

    console.log('🚀 开始预加载常用组件...')

    const preloadPromises = commonTypes.map(async (type) => {
      try {
        await loadFormComponent(type)
        console.log(`✅ 预加载完成: ${type}`)
      } catch (error) {
        console.warn(`⚠️ 预加载失败: ${type}`, error)
      }
    })

    await Promise.allSettled(preloadPromises)
    console.log('🎉 常用组件预加载完成')
  }

  // 清理缓存的方法
  const clearComponentCache = () => {
    componentCache.clear()
    Object.keys(componentLoadingStates).forEach(key => {
      delete componentLoadingStates[key]
    })
    performanceMetrics.cacheHits = 0
    performanceMetrics.cacheMisses = 0
    performanceMetrics.loadTimes = {}
    console.log('🧹 组件缓存已清理')
  }

  // 获取性能统计信息
  const getPerformanceStats = () => {
    const totalRequests = performanceMetrics.cacheHits + performanceMetrics.cacheMisses
    const cacheHitRate = totalRequests > 0 ? (performanceMetrics.cacheHits / totalRequests * 100).toFixed(2) : '0'

    return {
      ...performanceMetrics,
      totalRequests,
      cacheHitRate: `${cacheHitRate}%`,
      averageLoadTime: Object.keys(performanceMetrics.loadTimes).length > 0
        ? (Object.values(performanceMetrics.loadTimes).reduce((a, b) => a + b, 0) / Object.keys(performanceMetrics.loadTimes).length).toFixed(2) + 'ms'
        : '0ms'
    }
  }

  // 动态加载表单组件 - 优化版本
  const currentFormComponent = computed(() => {
    if (!formState.type) return null

    return markRaw(
      defineAsyncComponent({
        loader: () => loadFormComponent(formState.type),
        delay: 200, // 减少延迟时间
        timeout: 8000, // 增加超时时间
        errorComponent: ErrorComponent,
        loadingComponent: LoadingComponent,
        suspensible: false // 禁用 Suspense，使用自定义 loading
      })
    )
  })

  // 组件挂载时预加载常用组件
  onMounted(() => {
    // 延迟预加载，避免阻塞主要功能
    setTimeout(() => {
      preloadCommonComponents()
    }, 1000)
  })

  // 组件卸载时清理缓存（可选）
  onUnmounted(() => {
    // 在开发环境下清理缓存，生产环境保留缓存
    if (import.meta.env.DEV) {
      console.log('🔧 开发环境：清理组件缓存')
      clearComponentCache()
    }
  })

  // 优化后的 watch - 统一处理 props 变化
  watchEffect(() => {
    // 同步 props 到 formState
    formState.type = props.type
    formState.typeId = props.workflowTypeId
    formState.currentFormId = props.formId
    formState.visible = props.modelValue

    // 调试日志
    if (props.workflowTypeId) {
      console.log('FormManager 接收到 workflowTypeId:', props.workflowTypeId)
    }
  })

  // 处理表单显示逻辑 - 使用 watchPostEffect 确保 DOM 更新后执行
  watchPostEffect(() => {
    if (formState.visible && formComponentRef.value) {
      // 检查是否需要显示表单
      if (!isEditMode.value) {
        // 发起模式：没有预加载数据且没有当前表单ID
        console.log('FormManager: 发起表单，调用 showForm()')
        formComponentRef.value.showForm?.()
      } else {
        // 编辑模式：有预加载数据或已有表单ID
        console.log('FormManager: 编辑模式或已有数据，跳过 showForm 调用')
      }
    }
  })

  // 处理表单关闭时的清理
  watch(
    () => formState.visible,
    (newVisible, oldVisible) => {
      // 同步到父组件
      if (newVisible !== props.modelValue) {
        emit('update:modelValue', newVisible)
      }

      // 表单关闭时清理状态
      if (oldVisible && !newVisible) {
        formState.preloadedEditData = null
        formState.currentFormId = 0
        console.log('FormManager: 表单关闭，清理状态')
      }
    }
  )

  // 显示表单
  const showForm = async (formIdParam?: number | string): Promise<void> => {
    // 优先使用参数中的ID，其次使用props中的ID
    const idToUse = formIdParam || props.formId || formState.currentFormId
    console.log('FormManager.showForm - ID:', idToUse, '类型:', formState.type)

    // 检查是否有预加载的编辑数据，如果有则不重复请求
    if (formState.preloadedEditData) {
      console.log('FormManager: 使用预加载数据，跳过API请求')
      return
    }

    // 如果有表单ID，则先获取表单数据
    if (idToUse) {
      try {
        formState.loading = true
        const res = await ApplicationApi.detail(Number(idToUse))

        // HTTP拦截器已确保请求成功，直接处理数据
        if (res.code === ApiStatus.success) {
          // 更新表单类型和工作流ID
          formState.type = res.data.business_code || formState.type
          formState.typeId = res.data.definition_id || formState.typeId

          // 更新当前表单ID
          formState.currentFormId = Number(idToUse)

          // 使用 nextTick 替代 setTimeout，确保组件挂载完成
          await nextTick()
          if (formComponentRef.value?.showForm) {
            console.log('调用业务表单showForm方法，ID:', formState.currentFormId)
            formComponentRef.value.showForm(formState.currentFormId)
          } else {
            console.error('表单组件未挂载或没有showForm方法')
          }
        }
      } catch (error) {
        console.error('加载表单数据失败', error)
        // HTTP拦截器已处理错误提示
      } finally {
        formState.loading = false
      }
    } else if (formComponentRef.value?.showForm) {
      // 如果没有表单ID，则直接显示空表单
      console.log('显示空表单')
      formComponentRef.value.showForm()
    }
  }

  // 设置编辑数据 - 由父组件调用
  const setEditData = async (data: FormData & { formData?: any }): Promise<void> => {
    console.log('FormManager收到编辑数据:', data)

    // 立即设置当前表单ID，防止watch中的重复调用
    formState.currentFormId = data.id || 0
    formState.preloadedEditData = data

    // 准备传递给业务表单组件的数据
    const formDataWithId: FormData = {
      ...data.formData,
      id: data.id
    }

    // 尝试直接传递给业务表单组件
    if (formComponentRef.value?.setFormData) {
      console.log('直接传递表单数据到业务组件')
      formComponentRef.value.setFormData(formDataWithId)
      return
    }

    // 如果组件未挂载，等待下一个 tick
    console.warn('表单组件未挂载，等待组件挂载')
    await nextTick()

    if (formComponentRef.value?.setFormData) {
      formComponentRef.value.setFormData(formDataWithId)
      formState.preloadedEditData = null
    } else {
      // 最后的兜底处理，使用 watchPostEffect 监听组件挂载
      console.warn('组件仍未挂载，将在组件挂载后自动设置数据')
    }
  }

  // 统一处理表单提交
  const handleSubmitForm = async (formData: FormData): Promise<void> => {
    console.log('FormManager.handleSubmitForm 被调用，数据:', formData)
    if (formState.loading) return

    formState.loading = true

    try {
      const res = await ApplicationApi.submit({
        business_code: formState.type,
        definition_id: formState.typeId,
        business_data: formData,
        id: formData.id || 0
      })

      if (res.code === ApiStatus.success) {
        ElMessage.success(res.message || '申请提交成功')
        handleFormSuccess({
          type: 'submit',
          data: res.data
        })
      }
    } catch (error) {
      console.error('申请提交失败:', error)
      // HTTP拦截器已处理错误提示
    } finally {
      formState.loading = false
      // 重置子组件的loading状态
      if (formComponentRef.value?.submitting !== undefined) {
        formComponentRef.value.submitting = false
      }
    }
  }

  // 统一处理表单保存为草稿
  const handleSaveForm = async (formData: FormData): Promise<void> => {
    console.log('FormManager.handleSaveForm 被调用，数据:', formData)
    if (formState.loading) return

    formState.loading = true

    try {
      const res = await ApplicationApi.save({
        business_code: formState.type,
        definition_id: formState.typeId,
        business_data: formData,
        id: formData.id || 0
      })

      if (res.code === ApiStatus.success) {
        ElMessage.success(res.message || '草稿保存成功')

        // 更新表单数据（特别是ID）
        if (formComponentRef.value && res.data) {
          // 如果是新增，需要更新表单的ID
          if (!formData.id && res.data.instance_id) {
            formState.currentFormId = res.data.instance_id
            formComponentRef.value.updateFormData?.({
              id: res.data.instance_id,
              workflow_instance_id: res.data.instance_id,
              business_id: res.data.business_id
            })
          }
        }

        handleFormSuccess({
          type: 'save',
          data: res.data
        })
      }
    } catch (error) {
      console.error('草稿保存失败:', error)
      // HTTP拦截器已处理错误提示
    } finally {
      formState.loading = false
      // 重置子组件的loading状态
      if (formComponentRef.value?.saving !== undefined) {
        formComponentRef.value.saving = false
      }
    }
  }

  // 表单提交成功
  const handleFormSuccess = (data: FormSuccessData): void => {
    emit('success', {
      type: formState.type,
      ...data
    })
  }

  // 表单取消
  const handleFormCancel = (): void => {
    emit('cancel')
  }

  // 暴露方法给父组件
  defineExpose({
    showForm,
    setEditData,
    // 性能相关方法
    preloadCommonComponents,
    clearComponentCache,
    getPerformanceStats,
    // 状态查询
    isComponentLoading: (type: string) => componentLoadingStates[type] || false,
    isComponentCached: (type: string) => componentCache.has(type)
  })
</script>

<!--
统一业务表单API适配说明：

1. 所有业务表单组件（如leave-form.vue, travel-form.vue等）直接调用ApplicationApi中的方法：
   - 提交表单：ApplicationApi.submit(formData)
   - 保存草稿：ApplicationApi.save(formData)
   - 获取表单数据：ApplicationApi.detail(id)

2. 提交和保存时，需要确保表单数据中包含业务类型标识，如：
   formData = {
     ...表单数据,
     form_type: 'leave',  // 表单类型标识
     business_code: 'leave'  // 业务代码，与表单类型一致
   }

3. 表单组件与父组件的通信：
   - 表单组件提交/保存成功后，调用emit('success', {type: 'xxx', data: result})
   - 表单取消时，调用emit('cancel')

4. 表单数据加载逻辑：
   - 编辑模式时，通过ApplicationApi.detail(formId)获取数据
   - 发起模式时，初始化空表单

注意：各业务表单组件应当保持一致的接口和行为，以便统一管理和维护。
-->

<style scoped>
.form-loading-container {
  padding: 20px;
  text-align: center;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.loading-text {
  margin-top: 15px;
  color: #909399;
  font-size: 14px;
  font-weight: 500;
}

.form-error-container {
  padding: 20px;
  text-align: center;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.form-error-container .el-alert {
  margin-bottom: 15px;
}

/* 性能监控样式 */
.performance-stats {
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 9999;
  display: none; /* 默认隐藏，开发时可显示 */
}

.performance-stats.dev-mode {
  display: block;
}

.performance-stats .stat-item {
  margin: 2px 0;
}
</style>
