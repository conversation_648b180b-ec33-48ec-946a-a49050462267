<template>
  <ArtTableFullScreen>
    <div class="account-page" id="table-full-screen">
      <!-- 搜索栏 -->
      <ArtSearchBar
        v-model:filter="formFilters"
        :items="formItems"
        @reset="handleReset"
        @search="handleSearch"
      ></ArtSearchBar>

      <ElCard shadow="never" class="art-table-card">
        <!-- 表格头部 -->
        <ArtTableHeader
          :columnList="columnOptions"
          @refresh="handleRefresh"
        >
          <template #left>
            <ElButton
              v-auth="'system:permission_role:add'"
              @click="showDialog('add')"
              type="primary"
              v-ripple
              >新增
            </ElButton>
          </template>
        </ArtTableHeader>

        <!-- 表格 -->
        <ArtTable
          :loading="loading"
          :data="tableData"
          :currentPage="currentPage"
          :pageSize="pageSize"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :marginTop="10"
        >
          <template #default>
            <!-- 勾选列 -->
            <ElTableColumn type="selection" />

            <!-- 角色名称列 -->
            <ElTableColumn prop="name" label="角色名称" min-width="120" />

            <!-- 数据权限列 -->
            <ElTableColumn prop="data_scope" label="数据权限">
              <template #default="{ row }">
                {{ getDataScopeText(row.data_scope) }}
              </template>
            </ElTableColumn>

            <!-- 状态列 -->
            <ElTableColumn prop="status" label="状态">
              <template #default="{ row }">
                <ElTag :type="getTagType(row.status)">
                  {{ buildTagText(row.status) }}
                </ElTag>
              </template>
            </ElTableColumn>

            <!-- 排序列 -->
            <ElTableColumn prop="sort" label="排序" min-width="100" />

            <!-- 备注列 -->
            <ElTableColumn prop="remark" label="备注" min-width="150" />

            <!-- 创建时间列 -->
            <ElTableColumn prop="created_at" label="创建时间" sortable />

            <!-- 操作列 -->
            <ElTableColumn prop="operation" label="操作" width="230">
              <template #default="{ row }">
                <div>
                  <ArtButtonTable
                    v-if="hasAuth('system:permission_role:edit')"
                    text="编辑"
                    type="edit"
                    @click="showDialog('edit', row)"
                  />
                  <ArtButtonTable
                    v-if="hasAuth('system:permission_role:delete')"
                    text="删除"
                    type="delete"
                    @click="deleteRole(row.id)"
                  />
                </div>
              </template>
            </ElTableColumn>
          </template>
        </ArtTable>

        <ElDialog
          v-model="dialogVisible"
          :title="dialogType === 'add' ? '添加角色' : '编辑角色'"
          width="30%"
        >
          <ElForm
            ref="formRef"
            :model="formData"
            :rules="rules"
            :validate-on-rule-change="false"
            label-width="80px"
          >
            <ElFormItem label="角色名称" prop="name">
              <ElInput v-model="formData.name" />
            </ElFormItem>
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="状态" prop="status">
                  <ElSelect v-model="formData.status" style="width: 100%">
                    <ElOption label="正常" :value="1" />
                    <ElOption label="禁用" :value="0" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="排序" prop="sort">
                  <ElInput v-model="formData.sort" />
                </ElFormItem>
              </ElCol>
            </ElRow>
            <ElFormItem label="菜单权限" prop="menu_ids">
              <div class="menu-tree-container">
                <ElTree
                  ref="treeRef"
                  :data="menuTreeData"
                  :props="defaultProps"
                  node-key="id"
                  show-checkbox
                  :default-expanded-keys="expandedKeys"
                  :default-checked-keys="formData.menu_ids"
                  highlight-current
                  check-on-click-node
                  @check="handleTreeCheck"
                />
              </div>
            </ElFormItem>
            <ElFormItem label="数据权限" prop="data_scope">
              <ElSelect
                v-model="formData.data_scope"
                placeholder="请选择数据权限"
                style="width: 100%"
              >
                <ElOption label="全部" :value="1" />
                <ElOption label="本部门" :value="2" />
                <ElOption label="本部门及以下" :value="3" />
                <ElOption label="仅本人" :value="4" />
                <ElOption label="自定义" :value="5" />
              </ElSelect>
            </ElFormItem>

            <ElFormItem label="备注" prop="remark">
              <ElInput
                v-model="formData.remark"
                type="textarea"
                :rows="4"
                placeholder="请输入备注"
              />
            </ElFormItem>
          </ElForm>
          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="dialogVisible = false">取消</ElButton>
              <ElButton type="primary" @click="handleSubmit">提交</ElButton>
            </div>
          </template>
        </ElDialog>
      </ElCard>
    </div>
  </ArtTableFullScreen>
</template>

<script setup lang="ts">
  import { ElDialog, ElMessage, ElMessageBox, ElTag, ElTree } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import { RoleApi } from '@/api/roleApi'
  import { MenuApi } from '@/api/menuApi'
  import { SearchChangeParams, SearchFormItem } from '@/types/search-form'

  import ArtButtonTable from '@/components/core/forms/ArtButtonTable.vue'
  import { useAuth } from '@/composables/useAuth'
  import { ApiStatus } from '@/utils/http/status'

  // 权限验证
  const { hasAuth } = useAuth()

  onMounted(() => {
    getTableData()
    getMenuOptions()
  })

  const dialogType = ref('add')
  const dialogVisible = ref(false)
  const loading = ref(false)
  const menuTreeData = ref<any[]>([])
  const treeRef = ref<InstanceType<typeof ElTree> | null>(null)

  /*
   * 搜索
   * */
  const initialSearchState = {
    name: '',
    data_scope: '',
    status: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 搜索重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    currentPage.value = 1
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    currentPage.value = 1
    getTableData()
  }

  // 搜索表单项变更处理
  const handleFormChange = (params: SearchChangeParams): void => {
    console.log('表单项变更:', params)
  }

  // 搜索表单配置项
  const formItems: SearchFormItem[] = [
    {
      label: '角色名称',
      prop: 'name',
      type: 'input',
      config: {
        clearable: true
      },
      onChange: handleFormChange
    },
    {
      label: '数据权限',
      prop: 'data_scope',
      type: 'select',
      config: {
        clearable: true
      },
      options: () => [
        { label: '全部', value: 1 },
        { label: '本部门', value: 2 },
        { label: '本部门及以下', value: 3 },
        { label: '仅本人', value: 4 },
        { label: '自定义', value: 5 }
      ],
      onChange: handleFormChange
    },
    {
      label: '状态',
      prop: 'status',
      type: 'select',
      config: {
        clearable: true
      },
      options: () => [
        { label: '正常', value: 1 },
        { label: '禁用', value: 0 }
      ],
      onChange: handleFormChange
    }
  ]

  const getTagType = (status: number) => {
    switch (status) {
      case 1:
        return 'success'
      case 0:
        return 'danger'
      default:
        return 'info'
    }
  }

  // 构建标签文本
  const buildTagText = (status: number) => {
    return status === 1 ? '正常' : '禁用'
  }

  /*
   * 表格
   * */
  // 辅助函数
  const getDataScopeText = (dataScope: number) => {
    const scopeMap: Record<number, string> = {
      1: '全部',
      2: '本部门',
      3: '本部门及以下',
      4: '仅本人',
      5: '自定义'
    }
    return scopeMap[dataScope] || ''
  }

  const columnOptions = [
    { label: '勾选', type: 'selection' },
    { label: '角色名称', prop: 'name' },
    { label: '数据权限', prop: 'data_scope' },
    { label: '排序', prop: 'sort' },
    { label: '状态', prop: 'status' },
    { label: '备注', prop: 'remark' },
    { label: '创建时间', prop: 'created_at' },
    { label: '操作', prop: 'operation' }
  ]

  // 表格数据
  const tableData = ref<any[]>([])

  const currentPage = ref(1),
    pageSize = ref(10),
    total = ref(0)

  const getTableData = async () => {
    loading.value = true
    try {
      const params = {
        page: currentPage.value,
        limit: pageSize.value,
        ...formFilters
      }
      const res = await RoleApi.list(params)
      if (res.code === ApiStatus.success) {
        // 确保res.data是对象而非数组
        const data = res.data as any
        total.value = data.total || 0
        currentPage.value = data.currentPage || 1
        pageSize.value = data.pageSize || 10
        tableData.value = data.list || []
      }
    } catch (error) {
      console.error('获取角色列表失败', error)
    } finally {
      loading.value = false
    }
  }

  // 处理页码变化
  const handleCurrentChange = (page: number) => {
    currentPage.value = page
    getTableData()
  }

  // 处理每页条数变化
  const handleSizeChange = (size: number) => {
    pageSize.value = size
    currentPage.value = 1
    getTableData()
  }

  const handleRefresh = () => {
    getTableData()
  }

  // 表单实例
  const formRef = ref<FormInstance>()

  // 表单数据
  const formData = reactive({
    name: '',
    data_scope: undefined as number | undefined,
    data_scope_dept_ids: [],
    menu_ids: [] as number[],
    sort: 1,
    status: 1,
    remark: ''
  })

  // 表单验证规则
  const rules = reactive<FormRules>({
    name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
    data_scope: [{ required: true, message: '请选择数据权限', trigger: ['change', 'blur'] }]
  })

  const currentId = ref(0)
  const expandedKeys = ref<number[]>([])

  // 获取菜单选项
  const getMenuOptions = async () => {
    try {
      const res = await MenuApi.options()
      if (res.code === ApiStatus.success && res.data) {
        menuTreeData.value = res.data
        // 如果是编辑模式，需要设置展开节点
        if (dialogType.value === 'edit' && formData.menu_ids.length > 0) {
          expandedKeys.value = [...formData.menu_ids]
        }
      }
    } catch (error) {
      console.error('获取菜单选项失败', error)
    }
  }

  // 显示对话框
  const showDialog = async (type: string, row?: any) => {
    // 先显示对话框
    dialogVisible.value = true
    dialogType.value = type

    // 如果存在表单实例，则先重置，避免校验信息残留
    if (formRef.value) {
      formRef.value.resetFields()
    }

    // 重置表单数据
    currentId.value = 0
    formData.name = ''
    formData.remark = ''
    formData.status = 1
    formData.sort = 1
    formData.data_scope = undefined
    formData.menu_ids = []
    expandedKeys.value = []

    // 确保有菜单数据
    if (menuTreeData.value.length === 0) {
      await getMenuOptions()
    }

    // 如果是新增，确保清空树的选中状态
    if (type === 'add') {
      await nextTick(() => {
        if (treeRef.value) {
          treeRef.value.setCheckedKeys([])
        }
      })
    }

    if (type === 'edit' && row) {
      try {
        const res = await RoleApi.detail(row.id)
        if (res.code === ApiStatus.success && res.data) {
          const roleData = res.data
          // 使用nextTick避免数据更新触发校验
          await nextTick(() => {
            formData.name = roleData.name
            formData.remark = roleData.remark || ''
            formData.status = roleData.status
            formData.sort = roleData.sort
            formData.data_scope = roleData.data_scope || undefined
            currentId.value = roleData.id

            // 设置已选中的菜单ID，从menu_ids属性获取
            if (roleData.menu_ids && Array.isArray(roleData.menu_ids)) {
              formData.menu_ids = roleData.menu_ids
              // 将选中的菜单ID也设置为默认展开的节点
              expandedKeys.value = [...roleData.menu_ids]

              // 直接设置树的选中状态
              nextTick(() => {
                if (treeRef.value) {
                  treeRef.value.setCheckedKeys(formData.menu_ids)
                }
              })
            } else if (roleData.menus && Array.isArray(roleData.menus)) {
              // 兼容可能的另一种数据格式
              formData.menu_ids = roleData.menus
              // 将选中的菜单ID也设置为默认展开的节点
              expandedKeys.value = [...roleData.menus]

              // 直接设置树的选中状态
              nextTick(() => {
                if (treeRef.value) {
                  treeRef.value.setCheckedKeys(formData.menu_ids)
                }
              })
            } else {
              // 如果没有找到菜单数据，则清空
              formData.menu_ids = []
              expandedKeys.value = []
              // 确保清空树的选中状态
              nextTick(() => {
                if (treeRef.value) {
                  treeRef.value.setCheckedKeys([])
                }
              })
            }
          })
        }
      } catch (error) {
        console.error('获取角色详情失败', error)
      }
    }
  }

  // 删除角色
  const deleteRole = (id: number) => {
    ElMessageBox.confirm('确定要删除该角色吗？', '删除角色', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          const res = await RoleApi.delete([id])
          if (res.code === ApiStatus.success) {
            ElMessage.success('删除成功')
            await getTableData()
          }
        } catch (error) {
          console.error('删除角色失败', error)
        }
      })
      .catch(() => {
        // 取消删除
      })
  }

  // 菜单树配置
  const defaultProps = {
    children: 'children',
    label: 'name',
    value: 'id'
  }

  const showPermissionDialog = async (roleId: number) => {
    await showDialog('edit', { id: roleId })
  }

  // 处理节点选中状态变化
  const handleTreeCheck = (
    node: any,
    { checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys }: any
  ) => {
    console.log('选中节点变化:', node, checkedKeys)
    // 将选中的节点ID保存到formData.menu_ids
    formData.menu_ids = checkedKeys as number[]
  }

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    // 如果使用了ElTree，需要获取当前所有选中的节点ID
    if (treeRef.value) {
      formData.menu_ids = treeRef.value.getCheckedKeys() as number[]
    }

    await formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          // 确保提交的数据中包含menu_ids
          const submitData = { ...formData }

          // 确保menu_ids是数组
          if (!Array.isArray(submitData.menu_ids)) {
            submitData.menu_ids = []
          }

          // 如果使用了父子联动模式，提交时需要处理所选节点
          // 这里保留原始选择，后端可能会处理父子关系

          let res
          if (dialogType.value === 'add') {
            res = await RoleApi.add(submitData)
          } else {
            res = await RoleApi.edit(currentId.value, submitData)
          }

          if (res.code === ApiStatus.success) {
            ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功')
            dialogVisible.value = false
            await getTableData()
          }
        } catch (error) {
          console.error(dialogType.value === 'add' ? '添加角色失败' : '更新角色失败', error)
        }
      }
    })
  }
</script>

<style lang="scss" scoped>
  .page-content {
    .svg-icon {
      width: 1.8em;
      height: 1.8em;
      overflow: hidden;
      vertical-align: -8px;
      fill: currentcolor;
    }

    .el-col2 {
      display: flex;
      gap: 10px;
    }
  }

  .menu-tree-container {
    width: 100%;
    height: 220px;
    overflow-y: auto;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 5px;

    .el-tree {
      width: 100%;
      background: transparent;
    }

    .el-tree-node__content {
      height: 32px;
    }

    // 确保鼠标悬停样式
    .el-tree-node:hover > .el-tree-node__content {
      background-color: #f5f7fa;
    }

    // 选中节点的样式
    .el-tree-node.is-current > .el-tree-node__content {
      background-color: #f0f7ff;
      color: #409eff;
    }
  }

  :deep(.menu-tree-dropdown) {
    .el-select-dropdown__wrap {
      max-height: 220px !important;
    }

    .el-tree {
      min-width: 100%;
      display: inline-block !important;
    }

    .el-tree-node {
      white-space: nowrap !important;
    }

    .el-tree-node__content {
      height: 32px;
    }

    // 确保半选状态样式正确显示
    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      background-color: #409eff;
      border-color: #409eff;
    }

    .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
      content: '';
      position: absolute;
      display: block;
      background-color: #fff;
      height: 2px;
      transform: scale(0.5);
      left: 0;
      right: 0;
      top: 5px;
    }
  }

  :deep(.menu-tree-select) {
    width: 100%;
  }
</style>
