<template>
  <div class="performance-test-container">
    <h2>FormManager 性能测试面板</h2>
    
    <!-- 控制面板 -->
    <el-card class="control-panel">
      <h3>控制面板</h3>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-button @click="testComponentLoading" type="primary">
            测试组件加载
          </el-button>
        </el-col>
        <el-col :span="8">
          <el-button @click="preloadComponents" type="success">
            预加载组件
          </el-button>
        </el-col>
        <el-col :span="8">
          <el-button @click="clearCache" type="warning">
            清理缓存
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 性能统计 -->
    <el-card class="stats-panel">
      <h3>性能统计</h3>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="缓存命中次数">
          {{ stats.cacheHits }}
        </el-descriptions-item>
        <el-descriptions-item label="缓存未命中次数">
          {{ stats.cacheMisses }}
        </el-descriptions-item>
        <el-descriptions-item label="缓存命中率">
          {{ stats.cacheHitRate }}
        </el-descriptions-item>
        <el-descriptions-item label="平均加载时间">
          {{ stats.averageLoadTime }}
        </el-descriptions-item>
        <el-descriptions-item label="总请求次数">
          {{ stats.totalRequests }}
        </el-descriptions-item>
        <el-descriptions-item label="已缓存组件">
          {{ cachedComponents.join(', ') || '无' }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 测试表单 -->
    <el-card class="test-form">
      <h3>测试表单</h3>
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="表单类型">
          <el-select v-model="testForm.type" placeholder="选择表单类型">
            <el-option label="请假申请" value="leave" />
            <el-option label="出差申请" value="travel" />
            <el-option label="费用报销" value="expense" />
            <el-option label="采购申请" value="purchase" />
          </el-select>
        </el-form-item>
        <el-form-item label="工作流ID">
          <el-input-number v-model="testForm.workflowTypeId" :min="1" />
        </el-form-item>
        <el-form-item label="表单ID">
          <el-input-number v-model="testForm.formId" :min="0" />
        </el-form-item>
        <el-form-item>
          <el-button @click="showTestForm" type="primary">
            显示表单
          </el-button>
          <el-button @click="hideTestForm">
            隐藏表单
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- FormManager 组件 -->
    <FormManager
      ref="formManagerRef"
      v-model="testForm.visible"
      :type="testForm.type"
      :formId="testForm.formId"
      :workflowTypeId="testForm.workflowTypeId"
      @success="handleFormSuccess"
      @cancel="handleFormCancel"
    />

    <!-- 加载时间图表 -->
    <el-card class="chart-panel">
      <h3>加载时间统计</h3>
      <div id="loadTimeChart" style="height: 300px;"></div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import FormManager from './form-manager.vue'

// 测试表单数据
const testForm = reactive({
  type: '',
  workflowTypeId: 1,
  formId: 0,
  visible: false
})

// 性能统计数据
const stats = ref({
  cacheHits: 0,
  cacheMisses: 0,
  cacheHitRate: '0%',
  averageLoadTime: '0ms',
  totalRequests: 0
})

// 已缓存的组件列表
const cachedComponents = ref<string[]>([])

// FormManager 引用
const formManagerRef = ref()

// 更新统计数据
const updateStats = () => {
  if (formManagerRef.value) {
    const performanceStats = formManagerRef.value.getPerformanceStats()
    stats.value = performanceStats
    
    // 更新已缓存组件列表
    const types = ['leave', 'travel', 'expense', 'purchase']
    cachedComponents.value = types.filter(type => 
      formManagerRef.value.isComponentCached(type)
    )
  }
}

// 测试组件加载
const testComponentLoading = async () => {
  const testTypes = ['leave', 'travel', 'expense', 'purchase']
  
  for (const type of testTypes) {
    testForm.type = type
    testForm.visible = true
    
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    testForm.visible = false
    await new Promise(resolve => setTimeout(resolve, 500))
  }
  
  updateStats()
  ElMessage.success('组件加载测试完成')
}

// 预加载组件
const preloadComponents = async () => {
  if (formManagerRef.value) {
    await formManagerRef.value.preloadCommonComponents()
    updateStats()
    ElMessage.success('组件预加载完成')
  }
}

// 清理缓存
const clearCache = () => {
  if (formManagerRef.value) {
    formManagerRef.value.clearComponentCache()
    updateStats()
    ElMessage.success('缓存已清理')
  }
}

// 显示测试表单
const showTestForm = () => {
  if (!testForm.type) {
    ElMessage.warning('请先选择表单类型')
    return
  }
  testForm.visible = true
  setTimeout(updateStats, 1000)
}

// 隐藏测试表单
const hideTestForm = () => {
  testForm.visible = false
}

// 表单成功回调
const handleFormSuccess = (data: any) => {
  console.log('表单提交成功:', data)
  ElMessage.success('表单操作成功')
  updateStats()
}

// 表单取消回调
const handleFormCancel = () => {
  console.log('表单取消')
  testForm.visible = false
}

// 定时更新统计数据
onMounted(() => {
  const timer = setInterval(updateStats, 2000)
  
  onUnmounted(() => {
    clearInterval(timer)
  })
})
</script>

<style scoped>
.performance-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.control-panel,
.stats-panel,
.test-form,
.chart-panel {
  margin-bottom: 20px;
}

.control-panel .el-button {
  width: 100%;
}

h2 {
  text-align: center;
  margin-bottom: 20px;
  color: #409eff;
}

h3 {
  margin-bottom: 15px;
  color: #303133;
}
</style>
