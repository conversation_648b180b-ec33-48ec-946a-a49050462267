<?php
declare(strict_types=1);

namespace app\system\service;

use app\common\core\base\BaseService;
use app\common\exception\BusinessException;
use app\system\model\AttachmentCatModel;

/**
 * 附件分类服务类
 */
class AttachmentCatService extends BaseService
{
	
	protected function __construct()
	{
		$this->model = new AttachmentCatModel();
		parent::__construct();
	}
	
	/**
	 * 获取列表
	 *
	 * @param array $params 查询参数
	 * @return array
	 */
	public function getList(array $params): array
	{
		$where = [];
		
		// 排序规则
		$order = 'sort desc';
		
		// 不分页，返回所有
		$list = $this->getCrudService()
		             ->getList($where, $order);
		
		return list_to_tree($list->toArray());
	}
	
	
	/**
	 * 创建
	 *
	 * @param array $data 数据
	 * @return bool
	 */
	public function create(array $data, $adminId, $tenantId): bool
	{
		/*try {
			validate(DeptValidate::class)->check($data);
		}
		catch (ValidateFailedException $e) {
			throw new ValidateException($e->getMessage());
		}*/
		
		$data['parent_id']  = $data['parent_id'] ?? 0;
		$data['creator_id'] = $adminId;
		$data['tenant_id']  = $tenantId;
		
		return $this->getCrudService()
		            ->add($data);
	}
	
	/**
	 * 获取详情
	 *
	 * @param int $id ID
	 */
	public function getDetail(int $id)
	{
		$info = $this->getCrudService()
		             ->getOne([
			             [
				             'id',
				             '=',
				             $id
			             ],
		             ]);
		
		if ($info->isEmpty()) {
			throw new BusinessException('数据不存在');
		}
		
		return $info;
	}
	
	/**
	 * 更新
	 *
	 * @param int   $id   ID
	 * @param array $data 数据
	 * @return bool
	 */
	public function update(int $id, array $data): bool
	{
		/*try {
			validate(DeptValidate::class)->check($data);
		}
		catch (ValidateFailedException $e) {
			throw new ValidateException($e->getMessage());
		}*/
		
		if (isset($data['creator_id'])) {
			unset($data['creator_id']);
		}
		
		if (isset($data['tenant_id'])) {
			unset($data['tenant_id']);
		}
		
		return $this->getCrudService()
		            ->edit($data, ['id' => $id]);
	}
	
	/**
	 * 删除
	 *
	 * @param int $id ID
	 * @return bool
	 */
	public function delete(int $id): bool
	{
		
		$info = $this->getDetail($id);
		
		// 检查是否有子部门
		$childCount = $this->getCrudService()
		                   ->getCount([
			                   [
				                   'parent_id',
				                   '=',
				                   $id
			                   ],
		                   ]);
		if ($childCount > 0) {
			throw new BusinessException('请先删除子分类');
		}
		
		// 检查是否有图片关联
		$count = AttachmentService::getInstance()
		                          ->getCrudService()
		                          ->getCount([
			                          [
				                          'cate_id',
				                          '=',
				                          $info->cate_id
			                          ],
		                          ]);
		
		if ($count > 0) {
			throw new BusinessException('请先删除当前分类下图片');
		}
		
		// 执行删除
		return $info->delete();
	}
	
}