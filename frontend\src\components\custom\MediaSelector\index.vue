<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="60%"
    top="5vh"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <div class="media-selector">
      <!-- 左侧分类区域 -->
      <div class="category-panel" v-loading="catListLoading" :class="{ collapsed: isCollapsed }">
        <div class="category-header">
          <div class="header-left">
            <h3>{{ isCollapsed ? '' : '分类管理' }}</h3>
            <el-tooltip :content="isCollapsed ? '展开' : '折叠'" placement="top">
              <el-button type="primary" link class="collapse-btn" @click="toggleCollapse">
                <el-icon>
                  <component :is="isCollapsed ? 'Expand' : 'Fold'" />
                </el-icon>
              </el-button>
            </el-tooltip>
          </div>
          <div class="header-right" v-if="!isCollapsed">
            <el-button-group>
              <!--              <el-button
                              type="primary"
                              size="small"
                              icon="Plus"
                              v-if="hasAuth('system:attachment_category:add')"
                              @click="handleAddCategory()"
                            >
                              新增分类
                            </el-button>-->
              <el-tooltip :content="isTreeExpanded ? '折叠所有' : '展开所有'" placement="top">
                <el-button type="primary" size="small" @click="toggleTreeExpand">
                  <el-icon>
                    <component :is="isTreeExpanded ? 'ArrowUp' : 'ArrowDown'" />
                  </el-icon>
                </el-button>
              </el-tooltip>
            </el-button-group>
          </div>
        </div>
        <div class="category-list" v-show="!isCollapsed">
          <div class="special-categories">
            <div
              class="special-category-item"
              :class="{ active: currentCategoryId === 0 }"
              @click="handleCategorySelect('0')"
            >
              <span>全部</span>
            </div>
            <div
              class="special-category-item"
              :class="{ active: currentCategoryId === -1 }"
              @click="handleCategorySelect('-1')"
            >
              <span>未分类</span>
            </div>
          </div>
          <el-tree
            ref="treeRef"
            :key="treeKey"
            :data="categoryTreeData"
            :props="{
              label: 'name',
              children: 'children'
            }"
            :highlight-current="true"
            :current-node-key="activeCategory ? activeCategory.id.toString() : undefined"
            node-key="id"
            @node-click="handleTreeNodeClick"
          >
            <template #default="{ node }">
              <div class="custom-tree-node">
                <span class="category-name" :title="node.label">{{ node.label }}</span>
                <!--                <div class="category-actions">
                                  <el-button
                                    type="primary"
                                    link
                                    v-if="hasAuth('system:attachment_category:add')"
                                    class="action-btn"
                                    @click.stop="handleAddCategory(data)"
                                  >
                                    <el-icon>
                                      <Plus />
                                    </el-icon>
                                  </el-button>
                                  <el-button
                                    type="success"
                                    link
                                    v-if="hasAuth('system:attachment_category:edit')"
                                    class="action-btn"
                                    @click.stop="handleEditCategory(data)"
                                  >
                                    <el-icon>
                                      <Edit />
                                    </el-icon>
                                  </el-button>
                                  <el-button
                                    type="danger"
                                    link
                                    v-if="hasAuth('system:attachment_category:delete')"
                                    class="action-btn"
                                    @click.stop="handleDeleteCategory(data)"
                                  >
                                    <el-icon>
                                      <Delete />
                                    </el-icon>
                                  </el-button>
                                </div>-->
              </div>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 右侧媒体区域 -->
      <div class="media-panel" v-loading="listLoading">
        <div class="media-header">
          <div class="search-upload">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索文件名称"
              clearable
              class="search-input"
            >
              <template #prefix>
                <el-icon>
                  <Search />
                </el-icon>
              </template>
              <template #append>
                <el-button @click="handleSearch">
                  <el-icon>
                    <Search />
                  </el-icon>
                </el-button>
              </template>
            </el-input>
            <el-upload
              v-if="!uploadLoading"
              ref="uploadRef"
              :action="''"
              :http-request="customUpload"
              :before-upload="beforeUpload"
              :show-file-list="false"
              :accept="getAcceptTypes"
              multiple
            >
              <el-button type="primary" :loading="uploadLoading"
                >上传{{ getMediaTypeText }}
              </el-button>
            </el-upload>
            <el-button v-else type="primary" :loading="true">上传中...</el-button>
          </div>
          <div class="batch-actions">
            <el-button-group v-if="selectedMedia.length > 0">
              <!--              <el-button-->
              <!--                type="danger"-->
              <!--                @click="handleBatchDelete"-->
              <!--                >删除-->
              <!--              </el-button>-->
              <!--              <el-button @click="handleBatchMove"-->
              <!--                >移动-->
              <!--              </el-button>-->
              <el-button @click="clearAllSelections">
                <el-icon>
                  <Delete />
                </el-icon>
                清空选择 ({{ selectedMedia.length }})
              </el-button>
            </el-button-group>
          </div>
        </div>

        <!-- 媒体列表展示区 -->
        <div class="media-content">
          <el-empty v-if="mediaList.length === 0" description="暂无数据"></el-empty>
          <div v-else class="media-grid">
            <div
              v-for="item in mediaList"
              :key="item.id"
              :class="['media-item', { selected: item.checked }]"
            >
              <!-- 媒体预览 -->
              <div class="media-preview" @click.stop>
                <!-- 图片预览 -->
                <el-image
                  v-if="mediaType === 'image' && item.path"
                  :src="item.path"
                  fit="cover"
                  lazy
                  :preview-src-list="[item.path]"
                  :preview-teleported="true"
                  :initial-index="0"
                  :hide-on-click-modal="true"
                  :z-index="3000"
                  :infinite="false"
                  :zoom-rate="1.2"
                  :min-scale="0.2"
                  :max-scale="7"
                  @close="handlePreviewClose"
                />
                <!-- 视频预览 -->
                <video
                  v-if="mediaType === 'video' && item.path"
                  :src="item.path"
                  class="video-preview"
                  @click.stop
                />
                <!-- 音频预览 -->
                <div v-if="mediaType === 'audio'" class="audio-preview" @click.stop>
                  <el-icon>
                    <Headset />
                  </el-icon>
                  <audio :src="item.path" controls @click.stop></audio>
                </div>
                <!-- 文件预览 -->
                <div v-if="mediaType === 'file'" class="file-preview" @click.stop>
                  <el-icon>
                    <Document />
                  </el-icon>
                  <div class="file-ext">{{ getFileExtension(item.real_name) }}</div>
                </div>

                <!-- 存储类型角标 -->
                <div class="storage-badge">{{ item.storage }}</div>
              </div>

              <div class="media-info" @click="toggleSelect(item)">
                <div class="media-name" :title="item.name || item.real_name"
                  >{{ item.name || item.real_name }}
                </div>
                <div class="media-meta">
                  <div class="media-size">{{ item.size }}</div>
                  <div class="media-date-actions">
                    <span class="media-date">{{ item.created_at }}</span>
                    <!--                    <span class="delete-btn" @click.stop="handleDeleteMedia(item)">
                      <el-icon size="18"><Delete /></el-icon>
                    </span>-->
                    <!--                    <span class="view-btn" @click.stop="isPreview = true">
                      <el-icon size="18"><View /></el-icon>
                    </span>-->
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="media-actions">
                <el-checkbox v-model="item.checked" :value="true" @click.stop="handleCheckChange($event, item)" />
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
          <div class="selection-info" v-if="selectedMedia.length > 0">
            已选择 {{ selectedMedia.length }} 个{{ getMediaTypeText }}
          </div>
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.limit"
            :page-sizes="[12, 24, 36, 48]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <div class="footer-left">
          <!--          <div class="selected-count">
                      已选择 {{ selectedMedia.length }} 个文件
                      <template v-if="maxCount > 0"> (最多可选 {{ maxCount }} 个)</template>
                    </div>-->
          <!-- 上传提示信息 -->
          <div class="upload-tip" v-if="uploadTipText">
            <el-icon>
              <InfoFilled />
            </el-icon>
            <span>{{ uploadTipText }}</span>
          </div>
        </div>
        <!--        <div v-if="selectedMedia.length > 0" class="selected-preview">
                  <div class="preview-title">已选择预览：</div>
                  <div class="preview-items">
                    <template v-for="(item, index) in selectedMedia" :key="item.id">
                      <slot name="preview-item" :item="item" :index="index">
                        <div class="preview-item" :class="mediaType">
                          <img v-if="mediaType === 'image'" :src="item.path" :alt="item.name || item.real_name" />
                          <video v-else-if="mediaType === 'video'" :src="item.path" controls></video>
                          <audio v-else-if="mediaType === 'audio'" :src="item.path" controls></audio>
                          <div v-else-if="mediaType === 'file'" class="file-preview">
                            <el-icon><Document /></el-icon>
                            <span>{{ item.name || item.real_name }}</span>
                          </div>
                          <div class="preview-item-remove" @click.stop="removeSelectedItem(item)">
                            <el-icon><Close /></el-icon>
                          </div>
                        </div>
                      </slot>
                    </template>
                  </div>
                </div>-->
        <div class="footer-right">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确认</el-button>
        </div>
      </div>
    </template>

    <!-- 分类弹窗 -->
    <category-dialog
      v-model="categoryDialog.visible"
      :title="categoryDialog.title"
      :form="categoryDialog.form"
      :categories="categoryList"
      @submit="handleCategorySubmit"
    />

    <!-- 移动分类弹窗 -->
    <move-dialog
      v-model="moveDialog.visible"
      :categories="categoryList"
      @confirm="handleMoveConfirm"
    />
  </el-dialog>
</template>

<script lang="ts" setup>
  import { ElButton, ElMessage, ElUpload } from 'element-plus'
  import { Search, Headset, Document, Delete, InfoFilled } from '@element-plus/icons-vue'
  import { AttachmentCatApi } from '@/api/attachmentCatApi'
  import { AttachmentApi, AttachmentListResult } from '@/api/attachmentApi'
  import type { MediaItem, MediaCategory } from '../FormMediaSelector/MediaItem'
  import { useUploadStore } from '@/store/modules/upload'
  import { ApiStatus } from '@/utils/http/status'
  import type { ElTree } from 'element-plus'
  import { UploadService } from '@/utils/upload/UploadService'
  import { FileTypeUtil } from '@/utils/upload/FileTypeUtil'
  import CategoryDialog from './components/CategoryDialog.vue'
  import MoveDialog from './components/MoveDialog.vue'

  // 注册组件
  defineComponent({
    components: {
      CategoryDialog,
      MoveDialog
    }
  })

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false
    },
    // 媒体类型: 'image' | 'video' | 'audio' | 'file'
    mediaType: {
      type: String,
      default: 'image',
      validator: (value: string) => ['image', 'video', 'audio', 'file'].includes(value)
    },
    // 最大选择数量，0表示不限制
    maxCount: {
      type: Number,
      default: 0
    },
    // 初始已选择的媒体列表
    initialSelected: {
      type: Array as () => MediaItem[],
      default: () => []
    }
  })

  const emit = defineEmits(['update:modelValue', 'confirm', 'close', 'select'])

  // 对话框状态
  const dialogVisible = ref(props.modelValue)
  const title = computed(() => `选择${getMediaTypeText.value}`)

  // 媒体类型文字
  const getMediaTypeText = computed(() => {
    switch (props.mediaType) {
      case 'image':
        return '图片'
      case 'video':
        return '视频'
      case 'audio':
        return '音频'
      case 'file':
        return '文件'
      default:
        return '媒体'
    }
  })

  // const isPreview = ref(false)

  // 在 setup 中获取 uploadStore
  const uploadStore = useUploadStore()

  // 上传提示文字
  const uploadTipText = computed(() => {
    if (!uploadStore.uploadConfig) return ''

    const sizeLimit = uploadStore.uploadConfig.upload_allow_size || 10
    const typeText = getMediaTypeText.value

    // 获取允许的文件扩展名
    const exts = uploadStore.uploadConfig.upload_allow_ext_array || []

    // 根据媒体类型过滤相关扩展名
    let relevantExts: string[] = exts as string[]
    if (props.mediaType === 'image') {
      relevantExts = (exts as string[]).filter((ext) =>
        ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(ext.toLowerCase())
      )
    } else if (props.mediaType === 'video') {
      relevantExts = (exts as string[]).filter((ext) =>
        ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm', 'rmvb'].includes(ext.toLowerCase())
      )
    } else if (props.mediaType === 'audio') {
      relevantExts = (exts as string[]).filter((ext) =>
        ['mp3', 'wav', 'ogg', 'flac', 'aac', 'm4a', 'wma', 'ape'].includes(ext.toLowerCase())
      )
    }

    // 如果过滤后没有相关扩展名，使用所有扩展名
    if (relevantExts.length === 0) {
      relevantExts = exts as string[]
    }

    // 构建扩展名文本
    let extText = ''
    if (relevantExts.length > 0) {
      if (relevantExts.length > 6) {
        extText = relevantExts.slice(0, 6).join('、') + '等'
      } else {
        extText = relevantExts.join('、')
      }
    } else {
      extText = typeText
    }

    // 构建完整的提示文本
    let tipParts = []
    tipParts.push(`支持${extText}格式`)
    tipParts.push(`单个文件最大${sizeLimit}MB`)

    // 添加数量限制（如果有）
    if (props.maxCount > 0) {
      tipParts.push(`最多选择${props.maxCount}个${typeText}`)
    }

    return tipParts.join('，')
  })

  // 修改获取接受的文件类型的计算属性
  const getAcceptTypes = computed(() => {
    // 如果上传配置存在，则使用配置中的 MIME 类型
    if (uploadStore.uploadConfig && uploadStore.uploadConfig.upload_allow_mime) {
      // 根据媒体类型过滤 MIME 类型
      const mimeTypes = uploadStore.uploadConfig.upload_allow_mime

      // 从所有 MIME 类型中筛选出对应媒体类型的 MIME 类型
      switch (props.mediaType) {
        case 'image':
          // 筛选出所有图片类型的 MIME
          return mimeTypes.filter((mime) => mime.startsWith('image/')).join(',')
        case 'video':
          // 筛选出所有视频类型的 MIME
          return mimeTypes.filter((mime) => mime.startsWith('video/')).join(',')
        case 'audio':
          // 筛选出所有音频类型的 MIME
          return mimeTypes.filter((mime) => mime.startsWith('audio/')).join(',')
        case 'file':
          // 对于文件类型，可以排除图片、视频、音频类型
          return mimeTypes
            .filter(
              (mime) =>
                !mime.startsWith('image/') &&
                !mime.startsWith('video/') &&
                !mime.startsWith('audio/')
            )
            .join(',')
        default:
          return ''
      }
    }

    // 如果没有配置，则使用默认的 MIME 类型（保留原来的逻辑作为备选）
    switch (props.mediaType) {
      case 'image':
        return ''
      case 'video':
        return ''
      case 'audio':
        return ''
      case 'file':
        // 对于文件类型，可以不指定accept，允许所有类型
        return ''
      default:
        return ''
    }
  })

  // 上传组件引用
  const uploadRef = ref<InstanceType<typeof ElUpload> | null>(null)
  // 上传加载状态
  const uploadLoading = ref(false)

  // 修改相关变量的类型定义
  const categoryList = ref<MediaCategory[]>([])
  const activeCategory = ref<MediaCategory | null>(null)
  const mediaList = ref<MediaItem[]>([])
  const selectedMedia = ref<MediaItem[]>([])
  const searchKeyword = ref('')

  // 分页
  const pagination = ref({
    page: 1,
    limit: 24,
    total: 0
  })

  // 分类弹窗
  const categoryDialog = ref({
    visible: false,
    title: '',
    form: {
      id: 0,
      name: '',
      parent_id: 0
    }
  })

  // 移动弹窗
  const moveDialog = ref({
    visible: false,
    targetCategoryId: 0,
    selectedIds: [] as number[]
  })

  // 重命名弹窗
  /*const renameDialog = ref({
    visible: false,
    form: {
      id: 0,
      name: ''
    }
  })*/

  // 增加当前选择的分类ID，用于上传时设置分类
  const currentCategoryId = ref<number>(0)

  // 添加树的引用
  const treeRef = ref<InstanceType<typeof ElTree> | null>(null)
  // 添加树的 key，用于强制重新渲染
  const treeKey = ref(0)

  // 添加折叠状态
  const isCollapsed = ref(false)
  // 添加树展开状态
  const isTreeExpanded = ref(false)

  // 计算树形结构
  const categoryTreeData = computed(() => {
    if (!categoryList.value || categoryList.value.length === 0) {
      return []
    }

    // 如果返回的已经是树形结构，直接使用
    if (categoryList.value[0].children !== undefined) {
      return categoryList.value
    }

    // 否则转换成树形结构
    return buildCategoryTree(0, categoryList.value)
  })

  // 构建分类树
  const buildCategoryTree = (parentId: number, categories: MediaCategory[]): MediaCategory[] => {
    return categories
      .filter((item) => item.parent_id === parentId)
      .map((item) => {
        const children = buildCategoryTree(item.id, categories)
        return {
          ...item,
          children: children.length > 0 ? children : undefined
        }
      })
  }

  // 切换折叠状态
  const toggleCollapse = () => {
    isCollapsed.value = !isCollapsed.value
  }

  // 切换树的展开/折叠状态
  const toggleTreeExpand = () => {
    if (!treeRef.value) return

    isTreeExpanded.value = !isTreeExpanded.value

    if (isTreeExpanded.value) {
      // 展开所有节点
      expandAllNodes()
    } else {
      // 折叠所有节点
      collapseAllNodes()
    }
  }

  // 展开所有节点
  const expandAllNodes = () => {
    if (!treeRef.value) return

    // 使用 Element Plus 的 Tree 组件方法
    const expandRecursively = (data: MediaCategory[]) => {
      data.forEach((node) => {
        // 使用 node.id.toString() 确保 key 是字符串类型
        treeRef.value?.store.nodesMap[node.id.toString()]?.expand()
        if (node.children && node.children.length > 0) {
          expandRecursively(node.children)
        }
      })
    }

    expandRecursively(categoryList.value)
  }

  // 折叠所有节点
  const collapseAllNodes = () => {
    if (!treeRef.value) return

    // 保存当前选中的节点ID
    const currentNodeKey = activeCategory.value?.id?.toString()

    // 折叠所有节点
    const collapseRecursively = (data: MediaCategory[]) => {
      data.forEach((node) => {
        const nodeKey = node.id.toString()
        if (nodeKey !== currentNodeKey) {
          treeRef.value?.store.nodesMap[nodeKey]?.collapse()
        }
        if (node.children && node.children.length > 0) {
          collapseRecursively(node.children)
        }
      })
    }

    collapseRecursively(categoryList.value)
  }

  const catListLoading = ref(false)
  const listLoading = ref(false)

  // 监听对话框显示状态
  watch(
    () => props.modelValue,
    async (val) => {
      dialogVisible.value = val
      if (val) {
        // 对话框打开时初始化数据
        await fetchCategories()
        await fetchMediaList()
      }
    }
  )

  // 监听内部dialogVisible变化，同步到父组件
  watch(
    dialogVisible,
    (val) => {
      emit('update:modelValue', val)
    }
  )

  // 监听媒体类型变化
  watch(
    () => props.mediaType,
    () => {
      if (dialogVisible.value) {
        console.log('媒体类型变更，重新加载数据')
        fetchMediaList()
      }
    }
  )

  // 初始化数据
  /*const initData = async () => {
    try {
      console.log('开始初始化媒体选择器数据')
      // 重置分页
      pagination.value.page = 1

      // 加载分类列表
      await fetchCategories()
      console.log('分类列表加载完成', categoryList.value.length)

      // 加载媒体列表
      await fetchMediaList()
      console.log('媒体列表加载完成', mediaList.value.length)

      // 初始化已选择的媒体
      if (props.initialSelected && props.initialSelected.length > 0) {
        selectedMedia.value = props.initialSelected as MediaItem[]
        markSelectedMedia()
      } else {
        selectedMedia.value = []
      }
    } catch (error) {
      console.error('初始化媒体选择器数据失败:', error)
    }
  }*/

  // 初始化
  onMounted(() => {
    // 使用nextTick确保组件完全挂载后再执行
    nextTick(() => {
      console.log('MediaSelector 组件已挂载')
      if (dialogVisible.value) {
        // 如果对话框已经打开，则初始化数据
        fetchCategories().then(() => {
          fetchMediaList()
        })
      }
    })
  })

  // 组件销毁前清理资源
  onBeforeUnmount(() => {
    // 重置上传服务
    UploadService.reset()
  })

  // 自定义上传方法
  const customUpload = async (options: any) => {
    uploadLoading.value = true
    try {
      const file = options.file
      const catId =
        activeCategory.value?.id || (currentCategoryId.value === -1 ? 0 : currentCategoryId.value)

      // 检查上传文件数量限制
      if (uploadStore.uploadConfig) {
        // const maxCount = uploadStore.maxFileCount
        // 修改这里的判断逻辑，只有当实际超过限制时才提示错误
        // 注意：这里不应该使用mediaList.value.length，因为这是已有的文件数量
        // 而是应该检查当前已选择的文件数量加上要上传的文件是否超过限制
        if (props.maxCount > 0 && selectedMedia.value.length >= props.maxCount) {
          ElMessage.warning(`最多只能选择${props.maxCount}个${getMediaTypeText.value}`)
          uploadLoading.value = false
          return
        }
      }

      // 使用上传服务上传文件
      const result = await UploadService.uploadFile(file, props.mediaType, {
        cat_id: catId
        // 可以添加其他需要的参数
      })

      if (result.success) {
        ElMessage.success('上传成功')
        // 重新获取媒体列表
        await fetchMediaList()
        // 如果有成功回调，调用它
        if (options.onSuccess) {
          options.onSuccess(result.data)
        }
      } else {
        ElMessage.error(result.message || '上传失败')
        // 如果有失败回调，调用它
        if (options.onError) {
          options.onError(new Error(result.message))
        }
      }
    } catch (error: any) {
      ElMessage.error(error.message || '上传失败')
      // 如果有失败回调，调用它
      if (options.onError) {
        options.onError(error)
      }
    } finally {
      uploadLoading.value = false
    }
  }

  // 上传前检查
  const beforeUpload = (file: File) => {
    try {
      // 获取文件扩展名
      const extension = FileTypeUtil.getExtension(file.name)

      // 检查文件扩展名是否在允许列表中
      if (uploadStore.uploadConfig && uploadStore.uploadConfig.upload_allow_ext_array) {
        const allowedExtensions = uploadStore.uploadConfig.upload_allow_ext_array
        const isAllowedExtension = allowedExtensions.includes(extension.toLowerCase())

        if (!isAllowedExtension) {
          ElMessage.error(
            `不支持的文件类型: ${extension}，允许的类型: ${allowedExtensions.join(', ')}`
          )
          return false
        }
      }

      // 根据媒体类型检查文件
      const isCorrectType = checkFileTypeByMediaType(file, extension, props.mediaType)
      if (!isCorrectType) {
        ElMessage.error(`请上传正确的${getMediaTypeText.value}类型`)
        return false
      }

      // 检查文件大小
      const maxSizeMB = uploadStore.uploadConfig ? uploadStore.maxFileSize : 10 // 默认10MB
      const fileSizeInMB = file.size / (1024 * 1024)
      if (fileSizeInMB > maxSizeMB) {
        ElMessage.error(`文件大小不能超过${maxSizeMB}MB`)
        return false
      }

      return true
    } catch (error: any) {
      ElMessage.error(error.message || '文件验证失败')
      return false
    }
  }

  // 根据媒体类型检查文件类型
  const checkFileTypeByMediaType = (file: File, extension: string, mediaType: string): boolean => {
    // 如果有上传配置，则使用配置中的 MIME 类型
    if (uploadStore.uploadConfig && uploadStore.uploadConfig.upload_allow_mime) {
      const mimeTypes = uploadStore.uploadConfig.upload_allow_mime
      const fileMime = file.type

      // 检查文件的 MIME 类型是否在允许列表中
      const isAllowedMime = mimeTypes.includes(fileMime)

      if (!isAllowedMime) {
        console.warn(`文件MIME类型不在允许列表中: ${fileMime}`)
      }

      // 根据媒体类型检查
      switch (mediaType) {
        case 'image':
          return fileMime.startsWith('image/')
        case 'video':
          return fileMime.startsWith('video/')
        case 'audio':
          return fileMime.startsWith('audio/')
        case 'file':
          // 文件类型包括所有，但排除图片、视频、音频
          return (
            !fileMime.startsWith('image/') &&
            !fileMime.startsWith('video/') &&
            !fileMime.startsWith('audio/')
          )
        default:
          return true
      }
    }

    // 如果没有配置，则使用原来的逻辑
    switch (mediaType) {
      case 'image':
        return FileTypeUtil.isImage(extension)
      case 'video':
        return FileTypeUtil.isVideo(extension)
      case 'audio':
        return FileTypeUtil.isAudio(extension)
      case 'file':
        // 文件类型包括所有，但排除图片、视频、音频
        return (
          !FileTypeUtil.isImage(extension) &&
          !FileTypeUtil.isVideo(extension) &&
          !FileTypeUtil.isAudio(extension)
        )
      default:
        return true
    }
  }

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      catListLoading.value = true
      const res = await AttachmentCatApi.list()
      if (res.code === ApiStatus.success) {
        categoryList.value = res.data || []
        console.log('asdf', categoryList)
      }
    } catch (error) {
      console.error('获取分类失败', error)
    } finally {
      catListLoading.value = false
    }
  }

  // 获取媒体列表
  const fetchMediaList = async () => {
    try {
      listLoading.value = true
      const params = {
        page: pagination.value.page,
        limit: pagination.value.limit,
        cat_id:
          activeCategory.value?.id ||
          (currentCategoryId.value === -1 ? 0 : currentCategoryId.value),
        is_uncategorized: currentCategoryId.value === -1 ? 1 : 0,
        keyword: searchKeyword.value,
        media_type: props.mediaType
      }

      const res = await AttachmentApi.list(params)
      if (res.code === ApiStatus.success) {
        const result = res.data as AttachmentListResult

        // 适配新的数据结构
        const attachmentList = result.data || []

        // 将AttachmentData转换为MediaItem
        mediaList.value =
          attachmentList.map((item) => ({
            id: item.id,
            name: item.display_name,
            real_name: item.original_name,
            path: item.url || item.path,
            storage: item.storage,
            size: typeof item.size === 'number' ? item.size : 0,
            created_at: item.created_at,
            checked: false // 默认未选中
          })) || []

        pagination.value.total = result.total || 0

        // 标记已选中的媒体
        markSelectedMedia()
      }
    } catch (error) {
      console.error('获取媒体列表失败', error)
    } finally {
      listLoading.value = false
    }
  }

  // 标记已选择的媒体
  const markSelectedMedia = () => {
    if (selectedMedia.value.length > 0) {
      const selectedIds = selectedMedia.value.map((item) => item.id)
      mediaList.value.forEach((item) => {
        item.checked = selectedIds.includes(item.id)
      })
    } else {
      mediaList.value.forEach((item) => {
        item.checked = false
      })
    }
  }

  // 分类选择
  const handleCategorySelect = (index: string) => {
    const id = parseInt(index)
    currentCategoryId.value = id
    if (id === 0) {
      // 全部
      activeCategory.value = null
    } else if (id === -1) {
      // 未分类
      activeCategory.value = null
    } else {
      activeCategory.value = findCategoryById(categoryList.value, id)
    }

    pagination.value.page = 1
    fetchMediaList()
  }

  // 递归查找分类
  const findCategoryById = (categories: MediaCategory[], id: number): MediaCategory | null => {
    for (const category of categories) {
      if (category.id === id) {
        return category
      }
      if (category.children && category.children.length > 0) {
        const found = findCategoryById(category.children, id)
        if (found) return found
      }
    }
    return null
  }

  /*// 添加分类
const handleAddCategory = (parentCategory?: MediaCategory) => {
categoryDialog.value = {
  visible: true,
  title: parentCategory ? `添加"${parentCategory.name}"的子分类` : '新增顶级分类',
  form: {
    id: 0,
    name: '',
    parent_id: parentCategory ? parentCategory.id : 0
  }
}
}

// 编辑分类
const handleEditCategory = (category: MediaCategory) => {
categoryDialog.value = {
  visible: true,
  title: '编辑分类',
  form: {
    id: category.id,
    name: category.name,
    parent_id: category.parent_id || 0
  }
}
}

// 删除分类
const handleDeleteCategory = async (category: MediaCategory) => {
try {
  await ElMessageBox.confirm(`确定要删除分类"${category.name}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })

  const res = await AttachmentCatApi.delete(category.id)
  if (res.code === ApiStatus.success) {
    ElMessage.success('删除成功')

    // 如果删除的是当前选中的分类，则清除选中状态
    if (activeCategory.value?.id === category.id) {
      activeCategory.value = null
      fetchMediaList()
    }

    // 重新获取分类列表
    await fetchCategories()

    // 更新树的 key，强制重新渲染
    treeKey.value++
  } else {
    ElMessage.error(res.message || '删除失败')
  }
} catch (error) {
  // 用户取消删除
}
}*/

  // 提交分类表单
  const handleCategorySubmit = async (formData: {
    id: number
    name: string
    parent_id: number
  }) => {
    try {
      let res
      if (formData.id) {
        // 编辑
        res = await AttachmentCatApi.edit(formData.id, formData)
      } else {
        // 新增
        res = await AttachmentCatApi.add(formData)
      }

      if (res.code === ApiStatus.success) {
        ElMessage.success(formData.id ? '编辑成功' : '新增成功')
        categoryDialog.value.visible = false
        fetchCategories()
      } else {
        ElMessage.error(res.message || (formData.id ? '编辑失败' : '新增失败'))
      }
    } catch (error) {
      console.error('提交分类失败', error)
    }
  }

  // 搜索
  const handleSearch = () => {
    pagination.value.page = 1
    fetchMediaList()
  }

  // 判断媒体是否被选中
  /*const isSelected = (id: number) => {
    return selectedMedia.value.some((item) => item.id === id)
  }*/

  // 切换选择状态
  const toggleSelect = (item: MediaItem) => {
    const index = selectedMedia.value.findIndex((selected) => selected.id === item.id)

    // 判断是否为单图模式（maxCount === 1）
    const isSingleMode = props.maxCount === 1

    if (index > -1) {
      // 已选中，点击则取消选择
      selectedMedia.value.splice(index, 1)
      item.checked = false
    } else {
      // 未选中，准备选择

      // 单图模式下，先清空之前的选择
      if (isSingleMode) {
        // 清除所有已选项的选中状态
        mediaList.value.forEach((media) => {
          media.checked = false
        })
        // 清空已选媒体数组
        selectedMedia.value = []
      }

      // 检查是否超过最大选择数量限制
      if (props.maxCount > 0 && selectedMedia.value.length >= props.maxCount) {
        ElMessage.warning(`最多只能选择${props.maxCount}个${getMediaTypeText.value}`)
        return
      }

      // 添加新选择的项
      selectedMedia.value.push(item)
      item.checked = true
    }
  }

  // 选择框变化
  const handleCheckChange = (checked: boolean, item: MediaItem) => {
    // 判断是否为单图模式（maxCount === 1）
    const isSingleMode = props.maxCount === 1

    if (checked) {
      // 单图模式下，先清空之前的选择
      if (isSingleMode) {
        // 清除所有已选项的选中状态
        mediaList.value.forEach((media) => {
          media.checked = false
        })
        // 清空已选媒体数组
        selectedMedia.value = []
      }

      if (props.maxCount > 0 && selectedMedia.value.length >= props.maxCount) {
        ElMessage.warning(`最多只能选择${props.maxCount}个${getMediaTypeText.value}`)
        item.checked = false
        return
      }

      if (!selectedMedia.value.some((selected) => selected.id === item.id)) {
        selectedMedia.value.push(item)
      }
    } else {
      const index = selectedMedia.value.findIndex((selected) => selected.id === item.id)
      if (index > -1) {
        selectedMedia.value.splice(index, 1)
      }
    }
  }

  // 批量删除
  /*const handleBatchDelete = async () => {
    try {
      if (selectedMedia.value.length === 0) {
        ElMessage.warning('请先选择要删除的文件')
        return
      }

      await ElMessageBox.confirm(
        `确定要删除已选择的${selectedMedia.value.length}个文件吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      const ids = selectedMedia.value
        .map((item) => item.id)
        .filter((id) => id !== undefined) as number[]
      const res = await AttachmentApi.delete(ids)

      if (res.code === ApiStatus.success) {
        ElMessage.success('删除成功')
        selectedMedia.value = selectedMedia.value.filter((item) => !ids.includes(item.id as number))
        fetchMediaList()
      } else {
        ElMessage.error(res.message || '删除失败')
      }
    } catch (error) {
      // 用户取消删除
    }
  }*/

  // 批量移动
  /*const handleBatchMove = () => {
    if (selectedMedia.value.length === 0) {
      ElMessage.warning('请先选择要移动的文件')
      return
    }

    moveDialog.value = {
      visible: true,
      targetCategoryId: 0,
      selectedIds: selectedMedia.value
        .map((item) => item.id)
        .filter((id) => id !== undefined) as number[]
    }
  }*/

  // 移动确认
  const handleMoveConfirm = async (targetCategoryId: number) => {
    try {
      const res = await AttachmentApi.batchMove(moveDialog.value.selectedIds, targetCategoryId)

      if (res.code === ApiStatus.success) {
        ElMessage.success('移动成功')
        moveDialog.value.visible = false
        fetchMediaList()
      } else {
        ElMessage.error(res.message || '移动失败')
      }
    } catch (error) {
      console.error('移动文件失败', error)
    }
  }

  // 媒体操作
  /*const handleMediaAction = (command: string, item: MediaItem) => {
    switch (command) {
      case 'rename':
        const name = item.name || item.real_name || ''
        renameDialog.value = {
          visible: true,
          form: {
            id: (item.id as number) || 0,
            name: name
          }
        }
        break
      case 'move':
        moveDialog.value = {
          visible: true,
          targetCategoryId: 0,
          selectedIds: item.id !== undefined ? [item.id as number] : []
        }
        break
      case 'delete':
        handleDeleteMedia(item)
        break
    }
  }*/

  // 删除单个媒体
  /*const handleDeleteMedia = async (item: MediaItem) => {
    try {
      const itemName = item.name || item.real_name || '未命名'
      await ElMessageBox.confirm(`确定要删除文件"${itemName}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const id = item.id as number
      if (id === undefined) {
        ElMessage.error('无效的文件ID')
        return
      }

      const res = await AttachmentApi.delete([id])
      if (res.code === ApiStatus.success) {
        ElMessage.success('删除成功')

        // 从已选择列表中移除
        const index = selectedMedia.value.findIndex((selected) => selected.id === item.id)
        if (index > -1) {
          selectedMedia.value.splice(index, 1)
        }

        fetchMediaList()
      } else {
        ElMessage.error(res.message || '删除失败')
      }
    } catch (error) {
      // 用户取消删除
    }
  }*/

  // 分页相关
  const handleSizeChange = (): void => {
    fetchMediaList()
  }

  const handleCurrentChange = (): void => {
    fetchMediaList()
  }

  // 对话框关闭
  const handleClose = () => {
    emit('close')
  }

  // 确认选择
  const handleConfirm = () => {
    // 返回处理后的媒体项
    emit('confirm', selectedMedia.value)

    // 单个选择时也触发select事件
    if (selectedMedia.value.length === 1) {
      emit('select', selectedMedia.value[0])
    }

    dialogVisible.value = false
  }

  // 获取文件扩展名
  const getFileExtension = (filename: string = ''): string => {
    return filename.split('.').pop()?.toLowerCase() || ''
  }

  // 处理树节点点击
  const handleTreeNodeClick = (data: any) => {
    activeCategory.value = data
    // 重新加载媒体列表
    pagination.value.page = 1
    fetchMediaList()
  }

  // 添加清空所有选择的方法
  const clearAllSelections = () => {
    // 清空已选择的媒体
    selectedMedia.value = []
    // 更新当前页媒体的选中状态
    mediaList.value.forEach((item) => {
      item.checked = false
    })
  }

  // 处理预览关闭
  const handlePreviewClose = () => {
    console.log('图片预览已关闭')
    // 这里可以添加预览关闭后的处理逻辑
  }

  // 监听键盘事件，确保ESC键可以关闭预览
  onMounted(() => {
    const handleKeydown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        // 查找并关闭可能打开的图片预览
        const previewWrapper = document.querySelector('.el-image-viewer__wrapper')
        if (previewWrapper) {
          const closeBtn = previewWrapper.querySelector('.el-image-viewer__close')
          if (closeBtn) {
            (closeBtn as HTMLElement).click()
          }
        }
      }
    }

    document.addEventListener('keydown', handleKeydown)

    // 清理事件监听器
    onBeforeUnmount(() => {
      document.removeEventListener('keydown', handleKeydown)
    })
  })


</script>

<style lang="scss" scoped>
  .media-selector {
    display: flex;
    height: calc(80vh - 200px);
    max-height: 500px;
    font-size: 16px; /* 增加基础字体大小 */

    .category-panel {
      width: 300px; /* 增加左侧分类管理列表的宽度 */
      border-right: 1px solid #ebeef5;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      transition: all 0.3s ease;

      &.collapsed {
        width: 50px; /* 折叠时的宽度 */

        .category-header {
          justify-content: center;
          padding: 12px 0;

          .header-left {
            justify-content: center;
            width: 100%;

            h3 {
              display: none;
            }

            .collapse-btn {
              margin-left: 0;
            }
          }
        }
      }

      .category-header {
        padding: 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #ebeef5;
        transition: all 0.3s ease;

        .header-left {
          display: flex;
          align-items: center;

          h3 {
            margin: 0;
            font-size: 18px; /* 增加标题字体大小 */
            transition: opacity 0.3s;
          }

          .collapse-btn {
            margin-left: 8px;
            padding: 4px;

            .el-icon {
              font-size: 18px;
              color: var(--el-color-primary);
            }

            &:hover {
              background-color: var(--el-color-primary-light-9);
            }
          }
        }

        .header-right {
          .el-button {
            font-size: 15px; /* 增加按钮字体大小 */
          }
        }
      }

      .category-list {
        flex: 1;
        overflow-y: auto;

        .special-categories {
          padding: 5px 0;
          border-bottom: 1px solid #f0f0f0;

          .special-category-item {
            padding: 10px 20px; /* 增加内边距 */
            cursor: pointer;
            transition: all 0.3s;
            font-size: 16px; /* 增加字体大小 */

            &:hover {
              background-color: #f5f7fa;
            }

            &.active {
              color: var(--el-color-primary);
              background-color: var(--el-menu-hover-bg-color);
            }
          }
        }

        .custom-tree-node {
          width: calc(100% - 24px);
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-right: 0;
          box-sizing: border-box;

          .category-name {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-right: 8px;
            max-width: calc(100% - 85px); /* 调整以适应更宽的容器 */
            font-size: 16px; /* 增加字体大小 */
          }

          .category-actions {
            display: flex;
            opacity: 0;
            transition: opacity 0.3s;
            flex-shrink: 0;
            gap: 6px; /* 增加按钮之间的间距 */
            justify-content: flex-end;

            .action-btn {
              height: 22px; /* 增加按钮大小 */
              width: 22px;
              padding: 0;
              margin: 0;
              display: inline-flex;
              align-items: center;
              justify-content: center;

              .el-icon {
                font-size: 16px; /* 增加图标大小 */
              }

              &:hover {
                transform: scale(1.1);
              }
            }

            @media (hover: none) {
              /* 移动端设备始终显示按钮 */
              opacity: 0.8;
            }
          }
        }

        :deep(.el-tree-node__content) {
          height: 38px; /* 增加树节点高度 */
          overflow: hidden;
          padding-right: 8px !important;
          box-sizing: border-box;
          font-size: 16px; /* 增加字体大小 */
        }

        :deep(.el-tree-node__content:hover) {
          .category-actions {
            opacity: 1;
          }
        }
      }
    }

    .media-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 0 10px;

      .media-header {
        padding: 10px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #ebeef5;

        .search-upload {
          display: flex;
          gap: 10px;

          .search-input {
            width: 280px;

            :deep(input) {
              font-size: 15px;
            }
          }

          .el-button {
            font-size: 15px;
          }
        }

        .batch-actions {
          display: flex;
          gap: 10px;

          .el-button {
            font-size: 15px;

            .el-icon {
              margin-right: 4px;
            }
          }
        }
      }

      .media-content {
        flex: 1;
        overflow-y: auto;
        padding: 15px 0;

        .media-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
          gap: 15px;

          .media-item {
            border: 1px solid #ebeef5;
            border-radius: 4px;
            overflow: hidden;
            position: relative;
            transition: all 0.3s;
            cursor: pointer;

            &:hover,
            &.selected {
              border-color: var(--el-color-primary);
              box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

              .media-actions {
                opacity: 1;
              }
            }

            &.selected {
              background-color: rgba(var(--el-color-primary-rgb), 0.1);
            }

            .media-preview {
              height: 120px;
              display: flex;
              align-items: center;
              justify-content: center;
              overflow: hidden;
              background-color: #f5f7fa;
              position: relative;

              img {
                width: 100%;
                height: 100%;
                object-fit: contain;
              }

              .video-preview {
                width: 100%;
                height: 100%;
                object-fit: contain;
              }

              .audio-preview,
              .file-preview {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;

                .el-icon {
                  font-size: 40px;
                  margin-bottom: 10px;
                }
              }

              audio {
                width: 90%;
              }

              .storage-badge {
                position: absolute;
                top: 5px;
                left: 5px;
                background-color: rgba(0, 0, 0, 0.6);
                color: white;
                padding: 2px 6px;
                border-radius: 2px;
                font-size: 12px;
                z-index: 2;
              }
            }

            .media-info {
              padding: 8px;
              cursor: pointer;
              transition: background-color 0.2s;

              &:hover {
                background-color: rgba(var(--el-color-primary-rgb), 0.1);
              }

              .media-name {
                font-size: 15px; /* 增加字体大小 */
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                margin-bottom: 4px;
              }

              .media-meta {
                display: flex;
                flex-direction: column;
                gap: 4px;
                font-size: 14px; /* 增加字体大小 */

                .media-size,
                .media-date {
                  color: #909399;
                }

                .media-date-actions {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;

                  .delete-btn {
                    color: #f56c6c;
                    cursor: pointer;
                    font-size: 14px;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    &:hover {
                      color: #f78989;
                    }
                  }

                  .view-btn {
                    color: var(--el-color-primary);
                    cursor: pointer;
                    font-size: 14px;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    &:hover {
                      color: var(--el-color-primary-light-3);
                    }
                  }
                }
              }
            }

            .media-actions {
              position: absolute;
              top: 5px;
              right: 5px;
              opacity: 0;
              transition: opacity 0.3s;
              border-radius: 4px;
              z-index: 3;
            }
          }
        }
      }

      .pagination-container {
        padding: 10px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-top: 1px solid #ebeef5;

        .selection-info {
          color: var(--el-color-primary);
          font-size: 15px;
        }

        :deep(.el-pagination) {
          font-size: 15px;

          .el-pagination__sizes {
            .el-input {
              font-size: 15px;
            }
          }
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    font-size: 16px;
    flex-wrap: wrap;

    .footer-left {
      display: flex;
      flex-direction: column;
      gap: 8px;
      flex: 1;

      .selected-count {
        color: var(--el-color-primary);
        font-size: 15px;
      }

      .upload-tip {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 6px 10px;
        background-color: #f0f9ff;
        border: 1px solid #e1f5fe;
        border-radius: 4px;
        font-size: 12px;
        color: #0288d1;
        line-height: 1.4;
        max-width: 550px;
        text-align: left;

        .el-icon {
          font-size: 13px;
          color: #0288d1;
          flex-shrink: 0;
        }

        span {
          flex: 1;
        }
      }
    }

    .footer-right {
      display: flex;
      gap: 10px;
      align-items: flex-start;
    }

    .selected-preview {
      width: 100%;
      margin: 10px 0;

      .preview-title {
        font-weight: bold;
        margin-bottom: 8px;
      }

      .preview-items {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        max-height: 120px;
        overflow-y: auto;
        padding: 5px;

        .preview-item {
          position: relative;
          width: 80px;
          height: 80px;
          border: 1px solid #ebeef5;
          border-radius: 4px;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;

          &.image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          &.video video {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          &.audio {
            background-color: #f5f7fa;

            audio {
              width: 100%;
            }
          }

          &.file {
            background-color: #f5f7fa;
            flex-direction: column;
            padding: 5px;

            .el-icon {
              font-size: 24px;
              margin-bottom: 5px;
            }

            span {
              font-size: 12px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              width: 100%;
              text-align: center;
            }
          }

          .preview-item-remove {
            position: absolute;
            top: 2px;
            right: 2px;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            &:hover {
              background-color: rgba(0, 0, 0, 0.7);
            }

            .el-icon {
              font-size: 12px;
            }
          }
        }
      }
    }

    .el-button {
      font-size: 15px;
    }
  }

  /* 优化图片预览器的行为 */
  :deep(.el-image-viewer__wrapper) {
    z-index: 3000 !important;
  }

  :deep(.el-image-viewer__mask) {
    /* 确保遮罩层可以正确响应点击事件 */
    pointer-events: auto !important;
    cursor: pointer;
  }

  :deep(.el-image-viewer__canvas) {
    /* 防止图片拖动时干扰点击事件 */
    user-select: none;
  }

  :deep(.el-image-viewer__canvas img) {
    /* 确保图片本身不会阻止遮罩层的点击事件 */
    pointer-events: none;
  }
</style>
