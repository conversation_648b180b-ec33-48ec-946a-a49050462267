<?php

namespace app\common\lib\upload;

use app\system\service\AttachmentService;
use app\system\service\ConfigService;
use app\system\model\AttachmentModel;
use app\system\model\AttachmentUserModel;
use app\common\exception\BusinessException;
use think\facade\Event;
use think\facade\Db;
use think\facade\Config;

/**
 * 上传服务类 - 重构版本
 * 支持文件去重和用户权限隔离
 */
class UploadService
{
	
	/**
	 * 获取云存储上传Token
	 *
	 * @param string $storage  存储方式
	 * @param array  $params   参数
	 * @param int    $tenantId 租户ID
	 * @return array
	 * @throws \Exception
	 */
	public function getUploadToken(string $storage, int $tenantId, array $params = []): array
	{
		$driver = UploadStorageFactory::create($storage);
		$config = UploadStorageFactory::getConfig($storage, $tenantId);

		// 生成预上传ID，用于后续关联
		$preUploadId = $this->generatePreUploadId();

		// 添加回调URL和预上传ID
		$tokenParams = array_merge($params, [
			'tenant_id' => $tenantId,
			'pre_upload_id' => $preUploadId,
			'callback_url' => request()->domain() . '/api/upload/callback/' . $storage
		]);

		$result = $driver->getUploadToken($config, $tokenParams);

		return array_merge($result, [
			'storage' => $storage,
			'deadline' => $config['deadline'] ?? 3600,
			'pre_upload_id' => $preUploadId
		]);
	}

	/**
	 * 生成预上传ID
	 */
	private function generatePreUploadId(): string
	{
		return 'pre_' . uniqid() . '_' . time();
	}
	
	/**
	 * 本地文件上传
	 *
	 * @param array  $file     文件信息
	 * @param string $storage  存储方式
	 * @param int    $cateId   分类ID
	 * @param int    $tenantId 租户ID
	 * @param int    $userId   用户ID
	 * @return array
	 * @throws \Exception
	 */
	public function uploadFile(array $file, string $storage = 'local', int $cateId = 0, int $tenantId = 0, int $userId = 0): array
	{
		// 验证文件
		$this->validateFile($file);

		// 计算文件MD5
		$fileMd5 = md5_file($file['tmp_name']);

		return Db::transaction(function() use ($file, $storage, $cateId, $tenantId, $userId, $fileMd5) {

			// 查找是否存在相同文件
			$existingAttachment = AttachmentModel::findByMd5($storage, $fileMd5);

			if ($existingAttachment) {
				// 文件已存在，创建用户关联
				$userFile = $this->createUserFileAssociation($existingAttachment, $file, $cateId, $tenantId, $userId);
				$existingAttachment->incrementRefCount();

				$result = [
					'id' => $userFile->id,
					'attachment_id' => $existingAttachment->id,
					'name' => $existingAttachment->name,
					'display_name' => $userFile->display_name,
					'path' => $existingAttachment->path,
					'url' => getImgUrl($existingAttachment->path),
					'size' => $existingAttachment->size,
					'extension' => $existingAttachment->extension,
					'mime_type' => $existingAttachment->mime_type,
					'is_duplicate' => true,
					'upload_time' => $userFile->created_at
				];
			} else {
				// 文件不存在，上传新文件
				$result = $this->uploadNewFile($file, $storage, $cateId, $tenantId, $userId, $fileMd5);
			}

			// 触发上传完成事件
			Event::trigger('upload.after', $result);

			return $result;
		});
	}
	
	/**
	 * 处理云存储上传回调
	 *
	 * @param string $storage  存储方式
	 * @param array  $params   回调参数
	 * @param int    $tenantId 租户ID
	 * @param int    $userId   用户ID
	 * @return array
	 * @throws \Exception
	 */
	public function handleCallback(string $storage, array $params, int $tenantId = 0, int $userId = 0): array
	{
		$driver = UploadStorageFactory::create($storage);
		$config = UploadStorageFactory::getConfig($storage, $tenantId);

		// 验证回调数据
		$result = $driver->callback($params, $config);

		if (empty($result)) {
			throw new BusinessException('回调数据验证失败');
		}

		return Db::transaction(function() use ($result, $storage, $tenantId, $userId, $params) {

			// 提取文件信息
			$fileInfo = $this->extractFileInfoFromCallback($storage, $result);

			// 尝试通过storage_id查找现有文件（云存储的etag/hash）
			$existingAttachment = null;
			if (!empty($fileInfo['storage_id'])) {
				$existingAttachment = AttachmentModel::findByStorageId($storage, $fileInfo['storage_id']);
			}

			// 如果通过storage_id没找到，再通过MD5查找
			if (!$existingAttachment && !empty($fileInfo['file_md5'])) {
				$existingAttachment = AttachmentModel::findByMd5($storage, $fileInfo['file_md5']);
			}

			if ($existingAttachment) {
				// 文件已存在，创建用户关联
				$userFile = $this->createUserFileAssociationFromCallback($existingAttachment, $params, $tenantId, $userId);
				$existingAttachment->incrementRefCount();

				$finalResult = [
					'id' => $userFile->id,
					'attachment_id' => $existingAttachment->id,
					'name' => $existingAttachment->name,
					'display_name' => $userFile->display_name,
					'path' => $existingAttachment->path,
					'url' => getImgUrl($existingAttachment->path),
					'size' => $existingAttachment->size,
					'extension' => $existingAttachment->extension,
					'mime_type' => $existingAttachment->mime_type,
					'is_duplicate' => true,
					'upload_time' => $userFile->created_at
				];
			} else {
				// 文件不存在，创建新的物理文件记录
				$attachment = $this->createAttachmentFromCallback($fileInfo, $storage);
				$userFile = $this->createUserFileAssociationFromCallback($attachment, $params, $tenantId, $userId);

				$finalResult = [
					'id' => $userFile->id,
					'attachment_id' => $attachment->id,
					'name' => $attachment->name,
					'display_name' => $userFile->display_name,
					'path' => $attachment->path,
					'url' => getImgUrl($attachment->path),
					'size' => $attachment->size,
					'extension' => $attachment->extension,
					'mime_type' => $attachment->mime_type,
					'is_duplicate' => false,
					'upload_time' => $userFile->created_at
				];
			}

			// 触发上传完成事件
			Event::trigger('upload.after', $finalResult);

			return $finalResult;
		});
	}
	
	/**
	 * 删除文件
	 *
	 * @param int $id       附件ID
	 * @param int $tenantId 租户ID
	 * @return bool
	 * @throws \Exception
	 */
	public function deleteFile(int $id, int $tenantId = 0): bool
	{
		$attachmentService = AttachmentService::getInstance();
		
		$info = $attachmentService->getDetail($id, $tenantId);
		
		// 获取驱动和配置
		$driver = UploadStorageFactory::create($info['storage']);
		//		$config = UploadStorageFactory::getConfig($info['storage'], $tenantId);
		$config = [];
		// 删除文件
		$result = $driver->delete($info->getData('path'), $config);
		
		if ($result) {
			
			$result = $info->delete($id);
			
			// 触发删除完成事件
			Event::trigger('upload.delete', $info);
			
		}
		
		return $result;
	}
	
	/**
	 * 验证文件
	 *
	 * @param array $file 文件信息
	 * @throws \Exception
	 */
	private function validateFile(array $file): void
	{
		if (empty($file) || !isset($file['tmp_name']) || !file_exists($file['tmp_name'])) {
			throw new \Exception('文件不存在或已损坏');
		}
		
		// 验证文件大小
		$maxSize = Config::get('upload.max_size', 50 * 1024 * 1024); // 默认50MB
		if ($file['size'] > $maxSize) {
			throw new \Exception('文件大小超出限制');
		}
		
		// 验证文件类型
		$allowExt  = Config::get('upload.allow_ext', [
			'jpg',
			'jpeg',
			'png',
			'gif',
			'mp4',
			'zip',
			'pdf',
			'doc',
			'docx',
			'xls',
			'xlsx',
			'txt'
		]);
		$extension = pathinfo($file['name'], PATHINFO_EXTENSION);
		if (!in_array(strtolower($extension), $allowExt)) {
			throw new \Exception('不支持的文件类型');
		}
	}
	
	/**
	 * 查找已存在的文件
	 *
	 * @param string $fileMd5  文件MD5
	 * @param string $storage  存储方式
	 * @param int    $tenantId 租户ID
	 * @return array|null
	 */
	private function findExistingFile(string $fileMd5, string $storage, int $tenantId): ?array
	{
		$attachmentService = AttachmentService::getInstance();

		// 优先查找同租户下的文件
		$existingFile = $attachmentService->getModel()
		                                  ->where('file_md5', $fileMd5)
		                                  ->where('storage', $storage)
		                                  ->where('tenant_id', $tenantId)
		                                  ->where('deleted_at', null)
		                                  ->find();

		// 如果同租户下没有，查找共享文件
		if (!$existingFile) {
			$existingFile = $attachmentService->getModel()
			                                  ->where('file_md5', $fileMd5)
			                                  ->where('storage', $storage)
			                                  ->where('is_shared', 1)
			                                  ->where('deleted_at', null)
			                                  ->find();
		}

		return $existingFile ? $existingFile->toArray() : null;
	}

	/**
	 * 创建附件引用记录
	 *
	 * @param array $existingFile 已存在的文件记录
	 * @param array $file         新上传的文件信息
	 * @param int   $cateId       分类ID
	 * @param int   $tenantId     租户ID
	 * @return array
	 */
	private function createAttachmentReference(array $existingFile, array $file, int $cateId, int $tenantId): array
	{
		$attachmentService = AttachmentService::getInstance();

		// 增加引用计数
		$attachmentService->getModel()
		                  ->where('id', $existingFile['id'])
		                  ->inc('ref_count');

		// 创建新的附件记录
		$insertData = [
			'cate_id'    => $cateId,
			'name'       => $existingFile['name'],
			'real_name'  => $file['name'], // 使用新上传文件的原始名称
			'path'       => $existingFile['path'],
			'extension'  => $existingFile['extension'],
			'size'       => $existingFile['size'],
			'mime_type'  => $existingFile['mime_type'],
			'storage'    => $existingFile['storage'],
			'storage_id' => $existingFile['storage_id'],
			'file_md5'   => $existingFile['file_md5'],
			'is_shared'  => $existingFile['is_shared'],
			'ref_count'  => 1,
			'creator_id' => request()->user['id'] ?? 0,
			'tenant_id'  => $tenantId,
		];

		$newId = $attachmentService->getModel()->save($insertData);

		return array_merge($insertData, ['id' => $newId]);
	}

	/**
	 * 保存附件信息到数据库
	 *
	 * @param array $data 附件信息
	 * @return int
	 */
	private function saveAttachment(array $data): int
	{
		// 组装数据
		$insertData = [
			'cate_id'    => $data['cate_id'] ?? 0,
			'name'       => $data['name'],
			'real_name'  => $data['real_name'] ?? $data['name'],
			'path'       => $data['path'],
			'extension'  => $data['extension'],
			'size'       => $data['size'],
			'mime_type'  => $data['mime_type'],
			'storage'    => $data['storage'],
			'storage_id' => $data['storage_id'] ?? '',
			'file_md5'   => $data['file_md5'] ?? '',
			'is_shared'  => $data['is_shared'] ?? 0,
			'ref_count'  => $data['ref_count'] ?? 1,
			'creator_id' => request()->user['id'] ?? 0,
			'tenant_id'  => $data['tenant_id'] ?? 0,
		];

		return AttachmentService::getInstance()
		                        ->getModel()
		                        ->save($insertData);
	}

	/**
	 * 上传新文件
	 */
	private function uploadNewFile(array $file, string $storage, int $cateId, int $tenantId, int $userId, string $fileMd5): array
	{
		// 使用存储驱动上传文件
		$driver = UploadStorageFactory::create($storage);
		$config = UploadStorageFactory::getConfig($storage, $tenantId);

		$uploadResult = $driver->upload($file, $config);

		// 创建物理文件记录
		$attachment = AttachmentModel::create([
			'name' => $uploadResult['name'],
			'path' => $uploadResult['path'],
			'extension' => $uploadResult['extension'] ?? pathinfo($file['name'], PATHINFO_EXTENSION),
			'size' => $uploadResult['size'] ?? $file['size'],
			'mime_type' => $uploadResult['mime_type'] ?? $file['type'],
			'storage' => $storage,
			'storage_id' => $uploadResult['storage_id'] ?? '',
			'file_md5' => $fileMd5,
			'ref_count' => 1
		]);

		// 创建用户关联记录
		$userFile = $this->createUserFileAssociation($attachment, $file, $cateId, $tenantId, $userId);

		return [
			'id' => $userFile->id,
			'attachment_id' => $attachment->id,
			'name' => $attachment->name,
			'display_name' => $userFile->display_name,
			'path' => $attachment->path,
			'url' => getImgUrl($attachment->path),
			'size' => $attachment->size,
			'extension' => $attachment->extension,
			'mime_type' => $attachment->mime_type,
			'is_duplicate' => false,
			'upload_time' => $userFile->created_at
		];
	}

	/**
	 * 创建用户文件关联
	 */
	private function createUserFileAssociation(AttachmentModel $attachment, array $file, int $cateId, int $tenantId, int $userId): AttachmentUserModel
	{
		return AttachmentUserModel::createUserFileAssociation([
			'attachment_id' => $attachment->id,
			'user_id' => $userId,
			'tenant_id' => $tenantId,
			'cate_id' => $cateId,
			'display_name' => $file['name'],
			'original_name' => $file['name'],
			'upload_source' => 'web',
			'creator_id' => $userId
		]);
	}

	/**
	 * 从回调数据提取文件信息
	 */
	private function extractFileInfoFromCallback(string $storage, array $result): array
	{
		// 根据不同存储平台提取文件信息
		$fileInfo = [
			'name' => $result['name'] ?? '',
			'path' => $result['path'] ?? '',
			'size' => $result['size'] ?? 0,
			'extension' => $result['extension'] ?? '',
			'mime_type' => $result['mime_type'] ?? '',
			'storage_id' => $result['storage_id'] ?? $result['etag'] ?? $result['hash'] ?? '',
			'file_md5' => $result['file_md5'] ?? $result['md5'] ?? '',
			'storage_meta' => $result
		];

		return $fileInfo;
	}

	/**
	 * 从回调创建物理文件记录
	 */
	private function createAttachmentFromCallback(array $fileInfo, string $storage): AttachmentModel
	{
		return AttachmentModel::create([
			'name' => $fileInfo['name'],
			'path' => $fileInfo['path'],
			'extension' => $fileInfo['extension'],
			'size' => $fileInfo['size'],
			'mime_type' => $fileInfo['mime_type'],
			'storage' => $storage,
			'storage_id' => $fileInfo['storage_id'],
			'file_md5' => $fileInfo['file_md5'],
			'storage_meta' => $fileInfo['storage_meta'],
			'ref_count' => 1
		]);
	}

	/**
	 * 从回调创建用户文件关联
	 */
	private function createUserFileAssociationFromCallback(AttachmentModel $attachment, array $params, int $tenantId, int $userId): AttachmentUserModel
	{
		$originalName = $params['original_name'] ?? $params['name'] ?? $attachment->name;

		return AttachmentUserModel::createUserFileAssociation([
			'attachment_id' => $attachment->id,
			'user_id' => $userId,
			'tenant_id' => $tenantId,
			'cate_id' => $params['cate_id'] ?? 0,
			'display_name' => $originalName,
			'original_name' => $originalName,
			'upload_source' => 'cloud',
			'creator_id' => $userId
		]);
	}
}